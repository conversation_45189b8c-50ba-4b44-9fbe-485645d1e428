/**
* Copyright (c) 2020 kedacom
* OpenATC is licensed under Mulan PSL v2.
* You can use this software according to the terms and conditions of the Mulan PSL v2.
* You may obtain a copy of Mulan PSL v2 at:
* http://license.coscl.org.cn/MulanPSL2
* THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
* EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
* MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
* See the Mulan PSL v2 for more details.
**/
<template>
  <div class="basic-coodinfo-component">
    <el-form :inline="true" :model="basicCoodInfo" label-position="left">
      <el-form-item label="X:">
        <el-input-number :min="0" :controls="false" ref="refNumber-x" v-model="basicCoodInfo.x"
          @change="handleChangePosX" @input.native="eventChange('x')" />
      </el-form-item>
      <el-form-item label="Y:">
        <el-input-number :min="0" :controls="false" ref="refNumber-y" v-model="basicCoodInfo.y"
          @change="handleChangePosY" @input.native="eventChange('y')" />
      </el-form-item>
      <el-form-item v-if="showAngle" :label="$t('openatccomponents.channelizationmap.angle') + ':'">
        <el-input-number :min="0" :max="360" :precision="0" :step="1" :controls="false" ref="refNumber-angle" v-model="basicCoodInfo.angle"
          @change="handleChangeAngle" @input.native="eventChange('angle')" />
      </el-form-item>
      <el-form-item v-if="showKeyId" label="ID:">
        <el-input v-model="basicCoodInfo.keyid" ref="keyid" @input="handleChangeKeyID" />
      </el-form-item>
      <el-form-item v-if="showLaneId" :label="$t('openatccomponents.channelizationmap.holographiclaneid') + ':'">
        <el-input v-model="basicCoodInfo.laneid" ref="laneid" @input="handleChangeLaneID" />
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
export default {
  name: 'basic-coodinfo-component',
  components: {},
  data () {
    return {
      showAngle: true,
      showKeyId: true,
      showLaneId: true,
      basicCoodInfo: {
        x: 0,
        y: 0,
        angle: 0
      }
    }
  },
  props: {
    drawingObjInfo: {
      type: Object
    }
  },
  computed: {},
  watch: {
    drawingObjInfo: {
      handler: function (obj) {
        this.basicCoodInfo = {
          x: obj.x,
          y: obj.y,
          angle: obj.angle
        }
        if (obj.keyid !== undefined) {
          this.basicCoodInfo.keyid = obj.keyid
        }
        if (obj.laneid !== undefined) {
          this.basicCoodInfo.laneid = obj.laneid
        }
        this.isShowAngleSetting()
        this.isShowKeyIdSetting()
      },
      deep: true
    }
  },
  methods: {
    eventChange (field) {
      // 解决change事件只在失去焦点时响应，此处，可以监听到实时变化
      const key = this.$refs[`refNumber-${field}`].displayValue
      if (this.checkOnlyNum(key)) {
        this.basicCoodInfo[field] = field === 'angle' ? this.checkAngleMinMaxNum(key) : Number(key)
        this.$emit('handleChangeBasicCoord', this.basicCoodInfo)
      }
    },
    handleChangePosX (Xvalue) {
      // 解决用上下箭头控制数字改变
      if (this.checkOnlyNum(Xvalue)) {
        this.basicCoodInfo['x'] = Number(Xvalue)
        this.$emit('handleChangeBasicCoord', this.basicCoodInfo)
      }
    },
    handleChangePosY (Yvalue) {
      // 解决用上下箭头控制数字改变
      if (this.checkOnlyNum(Yvalue)) {
        this.basicCoodInfo['y'] = Number(Yvalue)
        this.$emit('handleChangeBasicCoord', this.basicCoodInfo)
      }
    },
    handleChangeAngle (angle) {
      // 解决用上下箭头控制数字改变
      if (this.checkOnlyNum(angle)) {
        this.basicCoodInfo['angle'] = this.checkAngleMinMaxNum(angle)
        this.$emit('handleChangeBasicCoord', this.basicCoodInfo)
      }
    },
    handleChangeKeyID (value) {
      this.basicCoodInfo.keyid = value
      this.$emit('handleChangeBasicCoord', this.basicCoodInfo)
    },
    handleChangeLaneID (value) {
      this.basicCoodInfo.laneid = value
      this.$emit('handleChangeBasicCoord', this.basicCoodInfo)
    },
    checkOnlyNum (numString) {
      // eslint-disable-next-line no-new-wrappers
      let num = new Number(numString)
      if (num.toString() === 'NaN') {
        // 校验输入非数字则不生效
        return false
      } else {
        return true
      }
    },
    checkAngleMinMaxNum (numString) {
      // 控制角度范围
      let num = Number(numString)
      if (num > 360) {
        return 360
      } else if (num < 0) {
        return 0
      } else {
        return num
      }
    },
    isShowAngleSetting () {
      this.showAngle = true
      if (this.drawingObjInfo.icontype === 'countdown') {
        this.showAngle = false
      }
      if (this.drawingObjInfo.icontype === 'detector' && this.drawingObjInfo.detailtype === 'detectorChart') {
        this.showAngle = false
      }
    },
    isShowKeyIdSetting () {
      this.showKeyId = false
      this.showLaneId = false
      if (this.drawingObjInfo.icontype === 'vehile') {
        this.showKeyId = true
        this.showLaneId = true
      }
      if (this.drawingObjInfo.icontype === 'ped') {
        this.showKeyId = true
      }
    }
  },
  mounted () {
    if (this.drawingObjInfo) {
      this.icontype = this.drawingObjInfo.icontype
      this.basicCoodInfo = {
        x: this.drawingObjInfo.x,
        y: this.drawingObjInfo.y,
        angle: this.drawingObjInfo.angle
      }
      if (this.drawingObjInfo.keyid !== undefined) {
        this.basicCoodInfo.keyid = this.drawingObjInfo.keyid
      }
      if (this.drawingObjInfo.laneid !== undefined) {
        this.basicCoodInfo.laneid = this.drawingObjInfo.laneid
      }
      this.isShowAngleSetting()
      this.isShowKeyIdSetting()
    }
  },
  destroyed () {}
}

</script>
<style lang="scss">
</style>
