/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="text" :class="{  'hasborder': bordervisible, 'noborder': !bordervisible}" @active="onActive()" v-if="!textActive">
    {{text}}
  </div>
  <textarea ref="area" v-model="text" class="textarea" @blur="onBlur" v-else>
  </textarea>
</template>

<script>
export default {
  data () {
    return {
      text: '',
      textActive: false,
      bordervisible: true
    }
  },
  props: {
    content: {
      type: String
    }
  },
  watch: {
    content: {
      handler: function (val) {
        this.text = val
        if (val === '') {
          this.bordervisible = true
        } else {
          this.bordervisible = false
        }
      }
    }
  },
  mounted () {
    this.text = this.content
    if (this.content === '') {
      this.bordervisible = true
    } else {
      this.bordervisible = false
    }
    this.$on('active', this.onActive)
  },
  methods: {
    onActive () {
      this.textActive = true
      this.$nextTick(() => {
        this.$refs.area.focus()
      })
    },
    onBlur () {
      this.textActive = false
      this.$parent.$emit('content-inactive')
      this.$emit('changeText', this.text)
    }
  }
}
</script>

<style>
  .text {
    width: 100%;
    height: 100%;
    font-size: 20px; /*默认字体大小*/
  }

  .hasborder {
    border: 1px solid  #ccc;
  }

  .noborder  {
    border: none;
  }

  .textarea {
    position: absolute;
    width: 100%;
    height: 100%;
  }
</style>
