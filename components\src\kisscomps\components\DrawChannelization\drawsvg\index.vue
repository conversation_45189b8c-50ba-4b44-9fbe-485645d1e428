/** * Copyright (c) 2020 kedacom * OpenATC is licensed under Mulan PSL v2. * You
can use this software according to the terms and conditions of the Mulan PSL v2.
* You may obtain a copy of Mulan PSL v2 at: *
http://license.coscl.org.cn/MulanPSL2 * THIS SOFTWARE IS PROVIDED ON AN "AS IS"
BASIS, WITHOUT WARRANTIES OF ANY KIND, * EITHER EXPRESS OR IMPLIED, INCLUDING
BUT NOT LIMITED TO NON-INFRINGEMENT, * MERCHANTABILITY OR FIT FOR A PARTICULAR
PURPOSE. * See the Mulan PSL v2 for more details. **/
<!--完整渠化编辑功能，包含导入-->
<template>
  <div class="custom-cross-part draw-channelization">
    <!-- <FirstImportCrossmap v-if="pageindex === 1"
    @loadSvgString="loadSvgString" @loadMapSize="loadMapSize" />
    <CustomDraw v-show="pageindex === 2" ref="CustomDraw"
      :AgentId="AgentId"
      :loadedChannelizatonData="loadedChannelizatonData"
      @saveCallback="saveCallback" /> -->
    <CustomDraw
      ref="CustomDraw"
      :AgentId="AgentId"
      :loadedChannelizatonData="loadedChannelizatonData"
      @saveCallback="saveCallback"
    />
  </div>
</template>
<script>
import FirstImportCrossmap from './firstImportCrossmap'
import CustomDraw from './index.draw'
import { getChannelizatonChart } from '../../../../api/cross'
import axios from 'axios'

export default {
  name: 'custom-channelization',
  components: {
    FirstImportCrossmap,
    CustomDraw
  },
  props: {
    AgentId: {
      type: String,
      default: '0'
    },
    tscParam: {
      type: Object
    }
  },
  data () {
    return {
      pageindex: 1, // 渠化编辑显示： 1 代表首次导入 2 代表已有保存的渠化图
      loadedChannelizatonData: {} // 接口返回的已保存路口渠化数据
    }
  },
  watch: {
    AgentId: {
      handler: function (val) {
        this.getChannelizatonChart()
      },
      deep: true
    },
    tscParam: {
      handler: function (val) {
        if (val) {
          this.$store.dispatch('SetTscParam', val)
        }
      },
      deep: true
    }
  },
  methods: {
    loadSvgString (type, imgstr) {
      this.pageindex = 2
      this.$nextTick(() => {
        this.$refs.CustomDraw.loadSvgString(type, imgstr)
      })
    },
    loadMapSize (type, mapsize) {
      this.$nextTick(() => {
        this.$refs.CustomDraw.loadMapSize(type, mapsize)
      })
    },
    getChannelizatonChart () {
      let _this = this
      _this.pageindex = 1
      getChannelizatonChart(this.AgentId)
        .then((data) => {
          if (!data.data.success) {
            // let parrenterror = getMessageByCode(data.data.code, _this.$i18n.locale)
            // if (data.data.data) {
            //   // 子类型错误
            //   let childErrorCode = data.data.data.errorCode
            //   if (childErrorCode) {
            //     let childerror = getMessageByCode(data.data.data.errorCode, _this.$i18n.locale)
            //     _this.$message.error(parrenterror + ',' + childerror)
            //   }
            // } else {
            //   _this.$message.error(parrenterror)
            // }
            this.AutoReadSVG()
            return
          }
          if (
            data.data.data === undefined ||
            JSON.stringify(data.data.data) === '{}'
          ) {
            this.AutoReadSVG()
            return
          }
          _this.pageindex = 2
          _this.loadedChannelizatonData = JSON.parse(
            JSON.stringify(data.data.data)
          )
        })
        .catch(() => {
          this.AutoReadSVG()
        })
    },
    saveCallback (res) {
      this.$emit('saveCallback', res)
    },
    AutoReadSVG () {
      axios.get('./img/CrossRoadsSvg.svg').then((val) => {
        if (!val.data || val.data === '') return
        this.loadedChannelizatonData = {
          crossMap: {
            index: -2,
            icontype: 'crossmap',
            x: 435,
            y: 325,
            w: 870,
            h: 650,
            angle: 0,
            imgfilesrc: '',
            svgstr: val.data,
            type: 'vectorgraph'
          }
        }
      })
    }
  },
  mounted () {
    this.getChannelizatonChart()
  }
}
</script>
<style scoped></style>
