# 交通信号控制计划管理页面 Vue3+TypeScript+ElementPlus 转换总结

## 原始页面功能分析

这是一个**交通信号控制计划管理页面**，主要功能包括：

### 核心功能
1. **计划管理**：添加、删除、编辑计划名称、一键排序
2. **协调控制**：协调计划开关控制
3. **时间设置**：小时和分钟选择（支持自动完成输入）
4. **控制类型**：多种控制方式选择（定周期控制、黄闪、全红、关灯等）
5. **方案选择**：根据控制类型动态启用/禁用方案选择
6. **周期显示**：显示选中方案的周期时间
7. **表格操作**：行点击选择、动态高度调整

### 数据结构
- **PlanItem**: 计划项目，包含id、hour、minute、control、pattern等字段
- **OptionItem**: 选项数据，包含value、label、cycle等字段
- **MinuteOption**: 分钟选项，特定的分钟值（0,5,10,15,20,25,30,35,40,45,50,55,59）

## 技术架构转换

### 从 Vue2 Options API 转换为 Vue3 Composition API + TypeScript

#### 1. 模板语法更新
- **插槽语法**: `slot-scope` → `#default`
- **事件处理**: 移除不必要的类型注释
- **组件引用**: `ref="planTable"` → `ref="planTableRef"`
- **按钮样式**: `type="text"` → `type="primary" link`

#### 2. 脚本部分重构

**导入和设置**:
```typescript
<script setup lang="ts">
import { ref, computed, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import { useStore } from 'vuex'
import { useI18n } from 'vue-i18n'
import { ElMessage, ElMessageBox } from 'element-plus'
```

**类型定义**:
```typescript
interface PlanItem {
  id: number
  hour: number
  minute: number | string
  control: number
  pattern?: number
}

interface OptionItem {
  value: number
  label: string
  cycle?: number
}
```

**Props定义**:
```typescript
interface Props {
  plan: PlanItem[]
  planid: number
  planname: string
  coordinate: number
}
const props = defineProps<Props>()
```

#### 3. 响应式数据转换

**从 data() 函数转换为 ref/reactive**:
```typescript
// Vue2
data() {
  return {
    tableHeight: 700,
    coorDinations: this.coordinate,
    // ...
  }
}

// Vue3
const tableHeight = ref(700)
const coorDinations = ref(props.coordinate)
const patternOptions = ref<OptionItem[]>([])
```

#### 4. 计算属性转换
```typescript
// Vue2
computed: {
  ...mapState({
    planList: state => state.globalParam.tscParam.planList
  })
}

// Vue3
const planList = computed(() => store.state.globalParam.tscParam.planList)
```

#### 5. 方法转换

**事件处理方法**:
```typescript
// Vue2
methods: {
  onAdd() {
    this.increaseId()
    // ...
  }
}

// Vue3
const onAdd = () => {
  increaseId()
  // ...
}
```

**消息提示更新**:
```typescript
// Vue2
this.$message({
  type: 'success',
  message: this.$t('edge.common.deletesucess')
})

// Vue3
ElMessage({
  type: 'success',
  message: t('edge.common.deletesucess')
})
```

#### 6. 生命周期钩子转换
```typescript
// Vue2
mounted() {
  this.setTableMaxHeight()
  document.addEventListener('click', this.handleClickOutside)
},
beforeDestroy() {
  document.removeEventListener('click', this.handleClickOutside)
}

// Vue3
onMounted(() => {
  initializePatternOptions()
  setTableMaxHeight()
  document.addEventListener('click', handleClickOutside)
})

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside)
})
```

#### 7. 监听器转换
```typescript
// Vue2
watch: {
  screenHeight: function() {
    this.tableHeight = window.innerHeight - document.querySelector('#footerBtn').offsetTop - 150
  }
}

// Vue3
watch(screenHeight, () => {
  const footerBtn = document.querySelector('#footerBtn') as HTMLElement
  if (footerBtn) {
    tableHeight.value = window.innerHeight - footerBtn.offsetTop - 150
  }
})
```

## 主要改进点

### 1. 类型安全
- 添加了完整的TypeScript类型定义
- 接口定义确保数据结构的一致性
- 类型检查减少运行时错误

### 2. 性能优化
- 使用Composition API提供更好的逻辑复用
- 响应式系统优化
- 更精确的依赖追踪

### 3. 代码组织
- 逻辑分组更清晰
- 函数式编程风格
- 更好的可维护性

### 4. 现代化特性
- 使用最新的Vue3语法
- ElementPlus组件库
- 更好的开发体验

## 兼容性说明

转换后的代码完全兼容原有功能，包括：
- 所有原有的交互逻辑
- 国际化支持
- Vuex状态管理
- 样式和布局

## 使用建议

1. 确保项目已升级到Vue3和ElementPlus
2. 配置TypeScript支持
3. 更新相关依赖包
4. 测试所有功能确保正常工作
