/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
<!-- 渠化图支持显示的元素类型 -->
  <div class="channelization-elements-components"
    :class="{'show-channelization-elements-components': UsageMode === 'show'}"
    :style="{'height': allitem.h + 'px', 'width': allitem.w + 'px'}">
    <drr
      id="sketchpadArea"
      :resizable="false"
      :x="allitem.x"
      :y="allitem.y"
      :w="allitem.w"
      :h="allitem.h"
      :angle="allitem.angle"
      :selectable="false"
      :aspectRatio="true"
    >
    <!-- 自定义底图 -->
    <CrossMap
      v-if="CrossMapVisible"
      :UsageMode="UsageMode"
      :CrossMapData="CrossMapData"
      :isSeletable="!isLockedCrossMap"
      :pointchange="pointchange"
      :chooseIndex="curChooseIconIndex"
      @changeCrossMap="changeCrossMap"
      @handleSelectIcon="handleSelectIcon"
    />
    <!-- 机动车图标 -->
    <div v-if="resetflag">
      <MotorwayIconSvg
      v-for="motorwaysitem in Motorways"
      :key="'motor-' + motorwaysitem.index"
      :UsageMode="UsageMode"
      :chooseIndex="curChooseIconIndex"
      :Data="motorwaysitem"
      :isSeletable="isSeletable"
      @changeMotorwayItem="changeMotorwayItem"
      @handleSelectIcon="handleSelectIcon"
    />
    </div>
    <!-- 文字图标 -->
    <!-- <EditableText
      :UsageMode="UsageMode"
      v-for="textitem in Texts"
      :key="textitem.index"
      :TextData="textitem"
      :isSeletable="isSeletable"
      @changeText="changeText"
    /> -->
    <!-- 倒计时图标 -->
    <CountdownIcon
      :UsageMode="UsageMode"
      v-for="timeitem in Countdown"
      :key="timeitem.index"
      :chooseIndex="curChooseIconIndex"
      :CountdownData="timeitem"
      :isSeletable="isSeletable"
      :CountdownList="CountdownList"
      :isHasPhase="isHasPhase"
      :bcgColor="bcgColor"
      :customText="customText"
      :textFontSize="textFontSize"
      @changeTimeItem="changeTimeItem"
      @handleSelectIcon="handleSelectIcon"
    />
    <!-- 人行横道图标 -->
    <div v-if="resetflag">
      <PedroadIconSvg
        :UsageMode="UsageMode"
        v-for="peditem in Pedwalk"
        :key="'ped-' + peditem.index"
        :chooseIndex="curChooseIconIndex"
        :PedData="peditem"
        :isSeletable="isSeletable"
        @changePedItem="changePedItem"
        @handleSelectIcon="handleSelectIcon"
      />
    </div>
    <DetectorIconSvg
      :UsageMode="UsageMode"
      v-for="detectoritem in Detector"
      :key="'detector-' + detectoritem.index"
      :chooseIndex="curChooseIconIndex"
      :DetectorData="detectoritem"
      :isSeletable="isSeletable"
      :CurChooseIcon="CurChooseIcon"
      @changeDetectorItem="changeDetectorItem"
      @handleSelectIcon="handleSelectIcon"
      />
    <!-- <DetectorChart
      :UsageMode="UsageMode"
      v-for="detchartitem in DetectorChart"
      :key="'detectorchart-' + detchartitem.index"
      :chooseIndex="curChooseIconIndex"
      :DetectorChartData="detchartitem"
      :Detector="Detector"
      :isSeletable="isSeletable"
      @changeDetectorChartItem="changeDetectorChartItem"
      @handleSelectIcon="handleSelectIcon" /> -->
  </drr>
  </div>
</template>

<script>
import CrossMap from './drawElement/crossMap'
import EditableText from './drawElement/editText'
import MotorwayIconSvg from './iconSvg/motorwayIconSvg'
import CountdownIcon from './iconSvg/countdownSvg'
import PedroadIconSvg from './iconSvg/pedroadIconSvg'
import DetectorIconSvg from './iconSvg/detectorIconSvg'
import DetectorChart from './iconSvg/detectorChart.vue'

export default {
  name: 'channelization-elements',
  components: {
    CrossMap,
    EditableText,
    MotorwayIconSvg,
    CountdownIcon,
    PedroadIconSvg,
    DetectorIconSvg,
    DetectorChart
  },
  props: {
    isSeletable: { // 元素是否可以编辑
      type: Boolean,
      default: true
    },
    allitem: {
      type: Object,
      required: true
    },
    CrossMapVisible: {
      type: Boolean,
      default: true
    },
    CrossMapData: {
      type: Object,
      required: true
    },
    pointchange: {
      type: Boolean,
      default: false
    },
    Motorways: {
      type: Array
    },
    Countdown: {
      type: Array
    },
    CountdownList: {
      type: Array
    },
    Pedwalk: {
      type: Array
    },
    curChooseIconIndex: {
      type: Number,
      default: -1
    },
    UsageMode: {
      type: String,
      default: 'draw'
    },
    isHasPhase: {
      type: Boolean,
      default: true
    },
    Detector: {
      type: Array
    },
    DetectorChart: {
      type: Array
    },
    CurChooseIcon: {
      type: Object
    },
    isLockedCrossMap: { // 是否锁定底图（是否允许编辑底图位置，角度等）
      type: Boolean,
      default: true
    },
    customText: {
      type: String
    },
    bcgColor: {
      type: String
    },
    textFontSize: {
      type: String
    }
  },
  data () {
    return {
      resetflag: true // 离线或者特殊控制后，控制行人相位、车道相位reset标识，主要是清空绿闪样式
    }
  },
  methods: {
    changeCrossMap (mapdata, fields) {
      this.$emit('changeCrossMap', mapdata, fields)
    },
    changeMotorwayItem (MotorwayItem, fields) {
      this.$emit('changeMotorwayItem', MotorwayItem, fields)
    },
    handleSelectIcon (iconobj, isCrossMap) {
      this.$emit('handleSelectIcon', iconobj, isCrossMap)
    },
    changeText (textobj, fields) {
      this.$emit('changeText', textobj, fields)
    },
    changeTimeItem (timeItem, fields) {
      this.$emit('changeTimeItem', timeItem, fields)
    },
    changePedItem (PedItem, fields) {
      this.$emit('changePedItem', PedItem, fields)
    },
    changeDetectorItem (detectorItem, fields) {
      this.$emit('changeDetectorItem', detectorItem, fields)
    },
    changeDetectorChartItem (detectorChartItem, fields) {
      this.$emit('changeDetectorChartItem', detectorChartItem, fields)
    },
    resetPhaseStatus () {
      // 车道相位、行人相位恢复默认状态
      this.resetflag = false
      this.$nextTick(() => {
        this.resetflag = true
      })
    }
  },
  created () {},
  mounted () {}
}
</script>
<style lang="scss" scoped>
.channelization-elements-components {
  position: relative;
}
.show-channelization-elements-components {
  left: 50%;
  top: 30px;
  transform: translateX(-50%);
  overflow: hidden;
}
</style>
