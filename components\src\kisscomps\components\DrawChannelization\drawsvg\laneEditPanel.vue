/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="lane-edit-panel">
    <!-- 车道编辑面板 -->
    <div class="vehile" v-if="Data.icontype === 'vehile'">
      <div class="edit-id">
        <span style="margin-right: 3px;">
          {{$t('openatccomponents.channelizationmap.vehiclelane') + ':'}}
        </span>
        <span>{{Data.id}}</span>
      </div>
      <div class="directions">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.laneturn')}}</div>
        <div class="each-icon" v-for="(item, index) in directionList" :key="index">
          <div class="single-icon"
          @click="selectDire(item.id)"
          :class="preselectDirection.indexOf(item.id) !== -1 ? 'single-icon-select' : ''">
            <svg-icon :icon-class="item.iconclass" className="direction-icon"></svg-icon>
          </div>
          <div class="single-icon-name">{{item.name}}</div>
        </div>
      </div>

      <div class="position">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.position')}}</div>
        <div class="position-options">
          <el-radio-group v-model="lanePosition" @change="selectLanePos">
            <el-radio :label="1">{{$t('openatccomponents.channelizationmap.eastward')}}</el-radio>
            <el-radio :label="2">{{$t('openatccomponents.channelizationmap.westward')}}</el-radio>
            <el-radio :label="3">{{$t('openatccomponents.channelizationmap.southward')}}</el-radio>
            <el-radio :label="4">{{$t('openatccomponents.channelizationmap.northward')}}</el-radio>
            <el-radio :label="5">{{$t('openatccomponents.channelizationmap.NE')}}</el-radio>
            <el-radio :label="6">{{$t('openatccomponents.channelizationmap.SE')}}</el-radio>
            <el-radio :label="7">{{$t('openatccomponents.channelizationmap.SW')}}</el-radio>
            <el-radio :label="8">{{$t('openatccomponents.channelizationmap.NW')}}</el-radio>
          </el-radio-group>
        </div>
      </div>

      <div class="lane-types">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.lanetype')}}</div>
        <div class="each-icon" v-for="(item, index) in lanetypeList" :key="index">
          <div class="single-icon"
          @click="selectLanetype(item.id)"
          :class="preselectLanetype === item.id ? 'single-icon-select' : ''">
            <svg-icon :icon-class="item.iconclass" className="type-icon"></svg-icon>
          </div>
          <div class="single-icon-name">{{item.name}}</div>
        </div>
        <div v-if="Data.controltype !== 0 && Data.controltype !== 1">
          <el-switch
          :value="flip"
          @change="handleChangeFilp"
          :active-text="$t('openatccomponents.channelizationmap.flipdisplay')">
        </el-switch>
        </div>
      </div>

      <div class="phase-associated">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.phaseassociated')}}</div>
        <!-- 相位仅可以关联选择，不可修改 -->
        <PhaseAssociatedComponent
          :editData="Data"
          @selectPhaseNew="selectPhaseNew" />
      </div>
      <div class="overlap-associated">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.overlapassociated')}}</div>
        <OverlapAssociatedComponent :editData="Data"
          @selectPhaseNew="selectPhaseNew"/>
      </div>
    </div>

    <!-- 行人编辑面板 -->
    <div class="ped" v-if="Data.icontype === 'ped'">
      <div class="edit-id">
        <span style="margin-right: 3px;">
          {{$t('openatccomponents.channelizationmap.sidewalk') + ':'}}
        </span>
        <span>{{Data.id}}</span>
      </div>
      <div class="ped-type">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.pedestriantype')}}</div>
        <div class="each-icon" v-for="(item, index) in pedestriantypeList" :key="index">
          <div class="single-icon"
          @click="selectPedType(item.id)"
          :class="Data.iconpedtypeid === item.id ? 'single-icon-select' : ''">
            <svg-icon :icon-class="item.iconclass" className="ped-icon"></svg-icon>
          </div>
          <div class="single-icon-name">{{item.name}}</div>
        </div>
      </div>
      <div class="ped-position">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.position')}}</div>
        <div class="each-icon" v-for="(item, index) in pedPosList" :key="index">
          <div class="single-icon"
          @click="selectPedPos(item.id)"
          :class="Data.iconpedposition === item.id ? 'single-icon-select' : ''">
            <svg-icon :icon-class="item.iconclass" className="ped-icon"></svg-icon>
          </div>
          <div class="single-icon-name">{{item.name}}</div>
        </div>
      </div>
      <div class="phase-associated">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.phaseassociated')}}</div>
        <!-- 相位仅可以关联选择，不可修改 -->
        <PhaseAssociatedComponent
          :editData="Data"
          @selectPhaseNew="selectPhaseNew" />
      </div>
      <div class="overlap-associated">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.overlapassociated')}}</div>
        <OverlapAssociatedComponent :editData="Data"
          @selectPhaseNew="selectPhaseNew"/>
      </div>
    </div>

    <!-- 检测器编辑面板 -->
    <div class="detector" v-if="Data.icontype === 'detector' && Data.detailtype === 'detector'">
      <div class="edit-id">
        <span style="margin-right: 3px;">
          {{$t('openatccomponents.channelizationmap.detector') + ':'}}
        </span>
        <span>{{Data.id}}</span>
      </div>
      <div class="detector-type">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.detectortype')}}</div>
        <div class="each-icon" v-for="(item, index) in detectortypeList" :key="index">
          <div class="single-icon"
          @click="selectDetectorType(item.id)"
          :class="Data.detectortype === item.id ? 'single-icon-select' : ''">
            <svg-icon :icon-class="item.iconclass" className="ped-icon"></svg-icon>
          </div>
          <div class="single-icon-name">{{item.name}}</div>
        </div>
      </div>
      <div class="detector-associated">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.detectorassociated')}}</div>
        <DetectorAssociatedComponent :editData="Data"
          @selectAssociatedDetector="selectAssociatedDetector"/>
      </div>
      <div class="detector-threshold" v-if="Data.detectortype === 1">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.detectorthreshold')}}</div>
        <el-form
           ref="threshold"
           label-position="left"
           label-width="130px">
           <el-form-item
                :label="$t('openatccomponents.channelizationmap.occupancythreshold') + ':'">
              <!-- <el-input-number :min="0" :max="100" :precision="0" :step="1" :controls="false"
                :value="occupancythreshold" size="mini"
                @change="handleChangeOccuthreshold" />
              <span class="detector-threshold-formtext">%</span> -->
              <el-select :value="occupancythreshold" @change="handleChangeOccuthreshold">
                <el-option
                  v-for="item in occupResholdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item
                :label="$t('openatccomponents.channelizationmap.saturationthreshold') + ':'">
              <!-- <el-input-number :min="0" :max="100" :precision="0" :step="1" :controls="false"
                :value="flowsaturationthreshold" size="mini"
                @change="handleChangeFlowthreshold" />
              <span class="detector-threshold-formtext">%</span> -->
              <el-select :value="minflowsaturationthreshold" @change="handleChangeMinFlowthreshold">
                <el-option
                  v-for="item in minflowResholdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled">
                </el-option>
              </el-select>
              <span class="flow-separator">-</span>
              <el-select :value="maxflowsaturationthreshold" @change="handleChangeMaxFlowthreshold">
                <el-option
                  v-for="item in maxflowResholdOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled">
                </el-option>
              </el-select>
            </el-form-item>
        </el-form>
      </div>
      <div class="detector-associated">
        <div class="tittle">{{$t('openatccomponents.channelizationmap.laneassociated')}}</div>
        <el-select filterable clearable :value="Data.associatedlaneid" @change="handleAssociatedLaneid">
          <el-option
            v-for="id in AssociatedLaneidOptions"
            :key="'Laneid' + id"
            :label="id"
            :value="id">
          </el-option>
        </el-select>
      </div>
    </div>
    <div class="basic-coord" v-if="JSON.stringify(Data) !== '{}' && Data.icontype !== 'countdown' && Data.icontype !== 'crossmap'">
      <div class="tittle">{{$t('openatccomponents.channelizationmap.basicinfo')}}</div>
      <BasicCoordInfo :drawingObjInfo="Data" @handleChangeBasicCoord="handleChangeBasicCoord" />
    </div>
    <div class="delete-drawed-item" v-if="JSON.stringify(Data) !== '{}' && Data.detailtype !== 'detectorChart'">
      <el-button type="primary" @click="handledelete" v-if="Data.icontype !== 'crossmap'">{{$t('openatccomponents.channelizationmap.delete')}}</el-button>
      <el-button type="primary" @click="handleClone" v-if="Data.icontype !== 'countdown' && Data.icontype !== 'crossmap'">{{$t('openatccomponents.channelizationmap.clone')}}</el-button>
    </div>
  </div>
</template>
<script>
import PhaseAssociatedComponent from './phaseAssociatedComponent.vue'
import OverlapAssociatedComponent from './overlapAssociatedComponent'
import DetectorAssociatedComponent from './detectorAssociatedComponent.vue'
import BasicCoordInfo from './basicCoordInfo'
export default {
  name: 'lane-edit-panel',
  components: {
    PhaseAssociatedComponent,
    OverlapAssociatedComponent,
    DetectorAssociatedComponent,
    BasicCoordInfo
  },
  data () {
    return {
      iconObj: {},
      preselectDirection: [1], // 预选方向
      preselectPedType: [1], // 预选行人类型
      directionList: [], // 业务下方向选择列表（非机动车没有掉头）
      allDirectionsList: [{
        id: 1,
        key: 'straightahead',
        iconclass: 'custom-straightahead',
        name: this.$t('openatccomponents.channelizationmap.straightahead')
      }, {
        id: 2,
        key: 'turnleft',
        iconclass: 'custom-turnleft',
        name: this.$t('openatccomponents.channelizationmap.turnleft')
      }, {
        id: 3,
        key: 'turnright',
        iconclass: 'custom-turnright',
        name: this.$t('openatccomponents.channelizationmap.turnright')
      }, {
        id: 4,
        key: 'turnaround',
        iconclass: 'custom-turnaround',
        name: this.$t('openatccomponents.channelizationmap.turnaround')
      }],
      pedestriantypeList: [{
        id: 1,
        key: 'pedestrian',
        iconclass: 'custom-pedestrian',
        name: this.$t('openatccomponents.channelizationmap.pedestrian')
      }, {
        id: 2,
        key: 'secondcrossing',
        iconclass: 'custom-secondcrossing',
        name: this.$t('openatccomponents.channelizationmap.secondcrossing')
      }, {
        id: 3,
        key: 'xpedestrian',
        iconclass: 'custom-xpedestrian',
        name: this.$t('openatccomponents.channelizationmap.xpedestrian')
      }, {
        id: 4,
        key: 'sectionpedestrian',
        iconclass: 'custom-sectionpedestrian',
        name: this.$t('openatccomponents.channelizationmap.sectionpedestrian')
      }],
      preselectLanetype: 0,
      flip: false, // 默认车道类型图标不反转，正向为西
      lanetypeList: [{
        id: 0, // id对于controltype
        key: 'vehiclemainroad',
        iconclass: 'custom-vehiclemainroad',
        name: this.$t('openatccomponents.channelizationmap.vehiclemainroad')
      }, {
        id: 1,
        key: 'vehiclebranch',
        iconclass: 'custom-vehiclebranch',
        name: this.$t('openatccomponents.channelizationmap.vehiclebranch')
      }, {
        id: 6,
        key: 'nonmotorizedlane',
        iconclass: 'custom-nonmotorizedlane',
        name: this.$t('openatccomponents.channelizationmap.nonmotorizedlane')
      }, {
        id: 3,
        key: 'buslane',
        iconclass: 'custom-buslane',
        name: this.$t('openatccomponents.channelizationmap.buslane')
      }, {
        id: 4,
        key: 'BRTlane',
        iconclass: 'custom-BRTlane',
        name: this.$t('openatccomponents.channelizationmap.BRTlane')
      }, {
        id: 5,
        key: 'tramlane',
        iconclass: 'custom-tramlane',
        name: this.$t('openatccomponents.channelizationmap.tramlane')
      }],
      lanePosition: 1, // 方位默认东向
      pedPosList: [], // 当前显示的方位列表，由类型决定
      pedCrossingPosList: [{
        id: 1,
        key: 'pedeastward',
        iconclass: 'custom-pedeastward',
        name: this.$t('openatccomponents.channelizationmap.eastward')
      }, {
        id: 2,
        key: 'pedwestward',
        iconclass: 'custom-pedwestward',
        name: this.$t('openatccomponents.channelizationmap.westward')
      }, {
        id: 3,
        key: 'pedsouthward',
        iconclass: 'custom-pedsouthward',
        name: this.$t('openatccomponents.channelizationmap.southward')
      }, {
        id: 4,
        key: 'pednorthward',
        iconclass: 'custom-pednorthward',
        name: this.$t('openatccomponents.channelizationmap.northward')
      }],
      pedSecondCrossingPosList: [{
        id: 5,
        key: 'east-top',
        iconclass: 'custom-east-top',
        name: this.$t('openatccomponents.channelizationmap.etped')
      }, {
        id: 6,
        key: 'east-bottom',
        iconclass: 'custom-east-bottom',
        name: this.$t('openatccomponents.channelizationmap.ebped')
      }, {
        id: 7,
        key: 'west-top',
        iconclass: 'custom-west-top',
        name: this.$t('openatccomponents.channelizationmap.wtped')
      }, {
        id: 8,
        key: 'west-bottom',
        iconclass: 'custom-west-bottom',
        name: this.$t('openatccomponents.channelizationmap.wbped')
      }, {
        id: 9,
        key: 'south-left',
        iconclass: 'custom-south-left',
        name: this.$t('openatccomponents.channelizationmap.slped')
      }, {
        id: 10,
        key: 'south-right',
        iconclass: 'custom-south-right',
        name: this.$t('openatccomponents.channelizationmap.srped')
      }, {
        id: 11,
        key: 'north-left',
        iconclass: 'custom-north-left',
        name: this.$t('openatccomponents.channelizationmap.nlped')
      }, {
        id: 12,
        key: 'north-right',
        iconclass: 'custom-north-right',
        name: this.$t('openatccomponents.channelizationmap.nrped')
      }],
      pedObliqueCrossingPosList: [{
        id: 13,
        key: 'xrped',
        iconclass: 'custom-xrped',
        name: this.$t('openatccomponents.channelizationmap.xrped')
      }, {
        id: 14,
        key: 'xlped',
        iconclass: 'custom-xlped',
        name: this.$t('openatccomponents.channelizationmap.xlped')
      }],
      pedSectionCrossingPosList: [{
        id: 15,
        key: 'snped',
        iconclass: 'custom-snped',
        name: this.$t('openatccomponents.channelizationmap.snped')
      }, {
        id: 16,
        key: 'ewped',
        iconclass: 'custom-ewped',
        name: this.$t('openatccomponents.channelizationmap.ewped')
      }],
      detectortypeList: [{
        id: 1,
        key: 'vehiclemainroad',
        iconclass: 'custom-detector',
        name: this.$t('openatccomponents.channelizationmap.vehicledetector')
      }, {
        id: 2,
        key: 'vehiclebranch',
        iconclass: 'custom-peddetector',
        name: this.$t('openatccomponents.channelizationmap.pedestriandetector')
      }],
      occupResholdOptions: [],
      minflowResholdOptions: [],
      maxflowResholdOptions: [],
      occupancythreshold: 80,
      minflowsaturationthreshold: 30,
      maxflowsaturationthreshold: 70,
      AssociatedLaneidOptions: []
    }
  },
  watch: {
    Data: {
      handler: function (data) {
        this.iconObj = JSON.parse(JSON.stringify(data))
        this.initDirOptions()
        if (data.icondireid !== undefined) {
          this.preselectDirection = JSON.parse(JSON.stringify(data.icondireid))
        }
        if (data.controltype !== undefined) {
          this.preselectLanetype = data.controltype
        }
        if (data.lanePosition !== undefined) {
          this.lanePosition = data.lanePosition
        }
        if (data.occupancythreshold !== undefined) {
          this.occupancythreshold = data.occupancythreshold
        }
        if (data.minflowsaturationthreshold !== undefined) {
          this.minflowsaturationthreshold = data.minflowsaturationthreshold
        }
        if (data.maxflowsaturationthreshold !== undefined) {
          this.maxflowsaturationthreshold = data.maxflowsaturationthreshold
        }
        if (data.flip !== undefined) {
          this.flip = data.flip
        }
        if (this.Data.icontype === 'detector' && this.Data.detailtype === 'detector') {
          this.handleDetectorSettings()
        }
        this.getCurPedPosList(data.iconpedtypeid)
      },
      deep: true
    }
  },
  props: {
    Data: {
      type: Object
    },
    Motorways: {
      type: Array
    }
  },
  methods: {
    initDirOptions () {
      this.directionList = JSON.parse(JSON.stringify(this.allDirectionsList))
    },
    selectDire (value) {
      let index = this.iconObj.icondireid.indexOf(value)
      if (index === -1) {
        this.iconObj.icondireid.push(value)
      } else {
        this.iconObj.icondireid.splice(index, 1)
      }
      this.$emit('handleChooseDire', this.iconObj)
    },
    selectLanetype (id) {
      this.iconObj.controltype = id
      this.$emit('changeIconDataByType', this.iconObj, ['controltype'])
    },
    selectPedType (value) {
      this.iconObj.iconpedtypeid = value
      this.getCurPedPosList(value)
      // 默认选择第一方位
      this.iconObj.iconpedposition = this.pedPosList[0].id
      this.$emit('handleChoosePed', this.iconObj)
    },
    selectDetectorType (value) {
      if (this.iconObj.detectortype !== undefined && this.iconObj.detectortype !== value) {
        this.iconObj.detectorid = undefined
      }
      this.iconObj.detectortype = value
      this.$emit('changeIconDataByType', this.iconObj, ['detectortype', 'detectorid'])
      if (value === 2) {
        // 行人检测器没有阈值设置
        this.iconObj.occupancythreshold = undefined
        this.iconObj.minflowsaturationthreshold = undefined
        this.iconObj.maxflowsaturationthreshold = undefined
        this.$emit('changeIconDataByType', this.iconObj, ['occupancythreshold', 'minflowsaturationthreshold', 'maxflowsaturationthreshold'])
      }
    },
    selectPedPos (value) {
      this.iconObj.iconpedposition = value
      this.$emit('handleChoosePed', this.iconObj)
    },
    handledelete () {
      this.$emit('deleteItem', this.iconObj)
    },
    selectLanePos (value) {
      this.iconObj.lanePosition = value
      this.$emit('changeIconDataByType', this.iconObj, ['lanePosition'])
    },
    selectPhase (phaseid, direction) {
      // 关联相位的同时，会修改相位方向
      this.iconObj.phaseid = phaseid
      this.iconObj.phaselabel = `P${phaseid}`
      this.iconObj.direction = direction
      this.$emit('changeIconDataByType', this.iconObj, ['phaseid', 'phaselabel', 'direction'])
    },
    selectPhaseNew (phaseid, phasetype) {
      // 仅关联相位，不修改原相位
      this.iconObj.phasetype = phasetype
      this.iconObj.phaseid = phaseid
      if (phasetype === 'phase') {
        this.iconObj.phaselabel = `P${phaseid}`
      }
      if (phasetype === 'overlap') {
        this.iconObj.phaselabel = `OP${phaseid}`
      }
      this.$emit('changeIconDataByType', this.iconObj, ['phaseid', 'phaselabel', 'phasetype'])
    },
    handleDisassociatePhase (deletePhaseid) {
      this.$emit('handleDisassociatePhase', deletePhaseid)
    },
    selectAssociatedDetector (detectorid) {
      // 仅关联检测器，不修改
      this.iconObj.detectorid = detectorid
      this.$emit('changeIconDataByType', this.iconObj, ['detectorid'])
    },
    handleChangeOccuthreshold (occupancythreshold) {
      if (occupancythreshold === undefined) {
        this.$message.error(this.$t('openatccomponents.channelizationmap.checkthreshold'))
        this.occupancythreshold = 80
        return
      }
      this.occupancythreshold = occupancythreshold
      this.iconObj.occupancythreshold = occupancythreshold
      this.$emit('changeIconDataByType', this.iconObj, ['occupancythreshold'])
    },
    handleChangeMinFlowthreshold (minflowsaturationthreshold) {
      if (minflowsaturationthreshold === undefined) {
        this.$message.error(this.$t('openatccomponents.channelizationmap.checkthreshold'))
        this.minflowsaturationthreshold = 30
        return
      }
      this.minflowsaturationthreshold = minflowsaturationthreshold
      this.iconObj.minflowsaturationthreshold = minflowsaturationthreshold
      this.$emit('changeIconDataByType', this.iconObj, ['minflowsaturationthreshold'])
      this.handleDisabledMaxflowOption()
    },
    handleDisabledMinflowOption () {
      // 此处需要排他，恢复上次置灰到默认值
      this.minflowResholdOptions = this.minflowResholdOptions.map(option => ({
        label: option.label,
        value: option.value
      }))
      // 控制流量饱和度最小阈值的禁用范围
      for (let i = 0; i < this.minflowResholdOptions.length; i++) {
        if (this.minflowResholdOptions[i].value >= this.maxflowsaturationthreshold) {
          this.minflowResholdOptions[i].disabled = true
        }
      }
    },
    handleChangeMaxFlowthreshold (maxflowsaturationthreshold) {
      if (maxflowsaturationthreshold === undefined) {
        this.$message.error(this.$t('openatccomponents.channelizationmap.checkthreshold'))
        this.maxflowsaturationthreshold = 70
        return
      }
      this.maxflowsaturationthreshold = maxflowsaturationthreshold
      this.iconObj.maxflowsaturationthreshold = maxflowsaturationthreshold
      this.$emit('changeIconDataByType', this.iconObj, ['maxflowsaturationthreshold'])
      this.handleDisabledMinflowOption()
    },
    handleDisabledMaxflowOption () {
      // 此处需要排他，恢复上次置灰到默认值
      this.maxflowResholdOptions = this.maxflowResholdOptions.map(option => ({
        label: option.label,
        value: option.value
      }))
      // 控制流量饱和度最大阈值的禁用范围
      for (let i = 0; i < this.maxflowResholdOptions.length; i++) {
        if (this.maxflowResholdOptions[i].value <= this.minflowsaturationthreshold) {
          this.maxflowResholdOptions[i].disabled = true
        }
      }
    },
    getCurPedPosList (iconpedtypeid) {
      // 方位根据行人类型显示
      switch (iconpedtypeid) {
        case 1: this.pedPosList = JSON.parse(JSON.stringify(this.pedCrossingPosList))
          break
        case 2: this.pedPosList = JSON.parse(JSON.stringify(this.pedSecondCrossingPosList))
          break
        case 3: this.pedPosList = JSON.parse(JSON.stringify(this.pedObliqueCrossingPosList))
          break
        case 4: this.pedPosList = JSON.parse(JSON.stringify(this.pedSectionCrossingPosList))
          break
      }
    },
    handleChangeFilp (value) {
      this.iconObj.flip = value
      this.$emit('changeIconDataByType', this.iconObj, ['flip'])
    },
    createResholdSelectOptions () {
      for (let i = 5; i < 100; i = i + 5) {
        let option = {
          label: i + '%',
          value: i
        }
        this.occupResholdOptions.push(option)
        if (i <= this.minflowsaturationthreshold) {
          option.disabled = true
        }
        this.maxflowResholdOptions.push(option)
      }
      for (let i = 5; i < 100; i = i + 5) {
        let option = {
          label: i + '%',
          value: i
        }
        if (i >= this.maxflowsaturationthreshold) {
          option.disabled = true
        }
        this.minflowResholdOptions.push(option)
      }
    },
    handleChangeBasicCoord (basicCoord) {
      // 基础信息面板修改
      this.iconObj.x = basicCoord.x
      this.iconObj.y = basicCoord.y
      this.iconObj.angle = basicCoord.angle
      this.$emit('changeIconDataByType', this.iconObj, ['x', 'y', 'angle'])
      if (basicCoord.keyid !== undefined) {
        this.iconObj.keyid = basicCoord.keyid
        this.$emit('changeIconDataByType', this.iconObj, ['keyid'])
      }
      if (basicCoord.laneid !== undefined) {
        this.iconObj.laneid = basicCoord.laneid
        this.$emit('changeIconDataByType', this.iconObj, ['laneid'])
      }
    },
    handleClone () {
      this.$emit('cloneItem', this.iconObj)
    },
    handleDetectorSettings () {
      this.handleDisabledMinflowOption()
      this.handleDisabledMaxflowOption()
      this.getAssociatedLaneidOptions()
    },
    handleAssociatedLaneid (value) {
      // 检测器关联车道索引
      this.iconObj.associatedlaneid = value
      this.$emit('changeIconDataByType', this.iconObj, ['associatedlaneid'])
    },
    getAssociatedLaneidOptions () {
      // 获取当前所有车道的id（即索引）
      let ids = this.Motorways.map(ele => ele.id)
      ids.sort(function (a, b) {
        return a - b
      })
      this.AssociatedLaneidOptions = ids
    }
  },
  created () {
    this.createResholdSelectOptions()
  },
  mounted () {
    if (JSON.stringify(this.Data) === '{}') return
    this.iconObj = JSON.parse(JSON.stringify(this.Data))
    if (this.Data.icondireid !== undefined) {
      this.preselectDirection = JSON.parse(JSON.stringify(this.Data.icondireid))
    }
    if (this.Data.controltype !== undefined) {
      this.preselectLanetype = this.Data.controltype
    }
    if (this.Data.lanePosition !== undefined) {
      this.lanePosition = this.Data.lanePosition
    }
    if (this.Data.occupancythreshold !== undefined) {
      this.occupancythreshold = this.Data.occupancythreshold
    }
    if (this.Data.minflowsaturationthreshold !== undefined) {
      this.minflowsaturationthreshold = this.Data.minflowsaturationthreshold
    }
    if (this.Data.maxflowsaturationthreshold !== undefined) {
      this.maxflowsaturationthreshold = this.Data.maxflowsaturationthreshold
    }
    if (this.Data.flip !== undefined) {
      this.flip = this.Data.flip
    }
    this.getCurPedPosList(this.Data.iconpedtypeid)
  }
}
</script>
<style scoped>
</style>
