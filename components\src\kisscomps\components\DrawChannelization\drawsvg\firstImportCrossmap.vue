/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="first-import-crossmap">
    <ImportDialog ref="importDialog" @loadSvgString="loadSvgString" @loadMapSize="loadMapSize" />
    <div class="import-btn">
      <el-button type="primary" @click="clickOpen">{{$t('openatccomponents.channelizationmap.importpicture')}}</el-button>
      <div class="tip">{{$t('openatccomponents.channelizationmap.importtip')}}</div>
    </div>
  </div>
</template>
<script>
import ImportDialog from './importDialog'
export default {
  name: 'first-import-crossmap',
  components: {
    ImportDialog
  },
  data () {
    return {
    }
  },
  watch: {
  },
  props: {
  },
  methods: {
    clickOpen () {
      this.$refs.importDialog.clickOpen()
    },
    loadSvgString (type, imgstr) {
      this.$emit('loadSvgString', type, imgstr)
    },
    loadMapSize (type, mapsize) {
      this.$emit('loadMapSize', type, mapsize)
    }
  },
  mounted () {}
}
</script>
<style scoped>
</style>
