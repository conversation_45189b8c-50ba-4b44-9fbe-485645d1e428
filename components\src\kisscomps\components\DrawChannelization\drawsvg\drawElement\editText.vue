/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
<div v-if="reset">
  <drr
    :x="item.x"
    :y="item.y"
    :w="item.w"
    :h="item.h"
    :angle="item.angle"
    :selectable="isSeletable"
    :aspectRatio="true"
    :hasActiveContent="isSeletable"
    @dragstop="boxDragStop(item, ...arguments)"
    @resizestop="boxResizeStop(item, ...arguments)"
    @rotatestop="boxRotateStop(item, ...arguments)"
  >
    <TextBox :content="TextData.text" @changeText="changeText" />
  </drr>
</div>
</template>

<script>
import TextBox from './TextBox'
export default {
  data () {
    return {
      reset: true,
      item: {}
    }
  },
  props: {
    TextData: {
      type: Object
    },
    isSeletable: {
      type: Boolean
    }
  },
  components: {
    TextBox
  },
  watch: {
    item: {
      handler: function (newval, oldval) {
        if ((JSON.stringify(oldval) !== '{}')) {
          // 更改原数据的位置大小数据
          let data = {
            ...this.TextData,
            ...newval
          }
          let fields = Object.keys(newval)
          this.$emit('changeText', data, fields)
        }
      }
    }
  },
  created () {
    this.IconW = this.TextData.w
    this.IconH = this.TextData.h
    this.item = {
      x: this.TextData.x,
      y: this.TextData.y,
      w: this.TextData.w,
      h: this.TextData.h,
      angle: this.TextData.angle
    }
  },
  methods: {
    changeText (text) {
      let textobj = JSON.parse(JSON.stringify(this.TextData))
      textobj.text = text
      this.$emit('changeText', textobj, ['text'])
    },
    boxDragStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
    },
    boxResizeStop (origin, final) {
      this.IconW = final.w + 'px'
      this.IconH = final.h + 'px'
      // this.resetSvg()
      this.item = JSON.parse(JSON.stringify(final))
    },
    boxRotateStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
    },
    resetSvg () {
      this.reset = false
      this.$nextTick(() => {
        this.reset = true
      })
    }
  }
}
</script>

<style>

</style>
