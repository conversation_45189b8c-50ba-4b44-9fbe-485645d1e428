/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div :id="Data.key" :style="{position: 'absolute', left: Data.left, top: Data.top}">
    <div :class="Data.id < 9 ? '' : 'hide'">
      <!-- <svg v-if="Data.id === 4 || Data.id === 8" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 18.1 14.5" :width="IconLenghDiaoTou" :height="IconWdithDiaoTou">
        <path id="东掉头" :class="Data.name === '东掉头' ? '' : 'invisible'" d="M4.876,3.054H8.5V0.039L16.07,4.276,8.411,8.513V5.5H4.792C3.53,5.5,2.436,6.883,2.436,8.513s1.094,3.015,2.357,3.015H18.006v2.444H4.876A5.2,5.2,0,0,1-.005,8.513,5.2,5.2,0,0,1,4.876,3.054Z" transform="translate(0 -0.031)" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
        <path id="西掉头" :class="Data.name === '西掉头' ? '' : 'invisible'" d="M13.124,10.964H9.5v3.028L1.93,9.736,9.589,5.48V8.508h3.619c1.262,0,2.357-1.391,2.357-3.028S14.47,2.452,13.208,2.452H-0.006V0H13.124c2.693,0,4.881,2.455,4.881,5.483S15.817,10.964,13.124,10.964Z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
      </svg> -->
      <svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
        viewBox="0 0 36 26" style="enable-background:new 0 0 36 26;" xml:space="preserve" :width="IconLengh" :height="IconWdith">
          <!-- 东相位 -->
          <path id="东左转" :class="Data.name === '东左转' ? '' : 'invisible'" d="M11,7.1h24v-3H10.9c-3.1,0-5.7,2.6-5.8,5.7l-0.4,2.9L1,12.2l3.9,9.7l6.5-8.2l-3.7-0.5l0.5-3.3
            C8.2,8.4,9.4,7.1,11,7.1L11,7.1z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <path id="东右转" :class="Data.name === '东右转' ? '' : 'invisible'" d="M8.1,15.8l-0.4-3l3.7-0.5L4.9,4.1L1,13.8l3.7-0.5l0.4,2.9c0,3.2,2.6,5.7,5.8,5.7H35v-3H10.9
            c-1.5,0-2.8-1.3-2.8-2.8V15.8z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <polygon id="东直行" :class="Data.name === '东直行' ? '' : 'invisible'" points="35,11.5 10.1,11.5 10.1,7.8 0.9,13 10.1,18.2 10.1,14.5 35,14.5 " :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <path id="东掉头" :class="Data.name === '东掉头' ? '' : 'invisible'" d="M1.1,14.5c0,3.7,2.6,6.7,5.8,6.7H35v-3H6.8c-1.5,0-2.8-1.7-2.8-3.7s1.3-3.7,2.8-3.7h4.3v3.7l9.1-5.2l-9-5.2v3.7H6.9C3.7,7.8,1.1,10.8,1.1,14.5z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <!-- 西相位 -->
          <path id="西左转" :class="Data.name === '西左转' ? '' : 'invisible'" d="M25,18.9H1v3h24.1c3.1,0,5.7-2.6,5.8-5.7l0.4-2.9l3.7,0.5l-3.9-9.7l-6.5,8.2l3.7,0.5l-0.5,3.3
            C27.8,17.6,26.6,18.9,25,18.9L25,18.9z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <path id="西右转" :class="Data.name === '西右转' ? '' : 'invisible'" d="M27.9,10.2l0.4,3l-3.7,0.5l6.5,8.2l3.9-9.7l-3.7,0.5l-0.4-2.9c0-3.2-2.6-5.7-5.8-5.7H1v3h24.1
            c1.5,0,2.8,1.3,2.8,2.8V10.2z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <polygon id="西直行" :class="Data.name === '西直行' ? '' : 'invisible'" points="1,14.5 26,14.5 26,18.2 35.1,13 26,7.8 26,11.5 1,11.5 " :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <path id="西掉头" :class="Data.name === '西掉头' ? '' : 'invisible'" d="M34.9,10.8c0-3.7-2.6-6.7-5.8-6.7H0.9v3h28.3c1.5,0,2.8,1.7,2.8,3.7s-1.3,3.7-2.8,3.7h-4.3v-3.7L15.8,16l9,5.2v-3.7h4.3C32.3,17.5,34.9,14.5,34.9,10.8z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
        </svg>
      </div>
      <div :class="Data.id >= 9 ? '' : 'hide'">
        <!-- <svg v-if="Data.id === 12 || Data.id === 16" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px" viewBox="0 0 14.5 18.1" :width="IconWdithDiaoTou" :height="IconLenghDiaoTou">
          <path id="南掉头" :class="Data.name === '南掉头' ? '' : 'invisible'"  d="M10.946,4.876V8.5h3.015L9.724,16.07,5.487,8.411H8.5V4.792c0-1.262-1.385-2.357-3.015-2.357S2.472,3.53,2.472,4.792V18.006H0.028V4.876A5.2,5.2,0,0,1,5.487-.005,5.2,5.2,0,0,1,10.946,4.876Z" transform="translate(-0.031)" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <path id="北掉头" :class="Data.name === '北掉头' ? '' : 'invisible'" d="M3.036,13.124V9.5H0.008L4.264,1.93,8.52,9.589H5.492v3.619c0,1.262,1.391,2.357,3.028,2.357s3.028-1.094,3.028-2.357V-0.006H14V13.124c0,2.693-2.455,4.881-5.483,4.881S3.036,15.817,3.036,13.124Z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
        </svg> -->
        <svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" x="0px" y="0px"
          viewBox="0 0 26 36" style="enable-background:new 0 0 26 36;" xml:space="preserve" :width="IconWdith" :height="IconLengh">
          <!-- 南相位 -->
          <path id="南左转" :class="Data.name === '南左转' ? '' : 'invisible'" d="M18.9,11v24h3V10.9c0-3.1-2.6-5.7-5.7-5.8l-2.9-0.4L13.8,1L4.1,4.9l8.2,6.5l0.5-3.7l3.3,0.5
            C17.6,8.2,18.9,9.4,18.9,11L18.9,11z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <path id="南右转" :class="Data.name === '南右转' ? '' : 'invisible'" d="M10.2,8.1l3-0.4l0.5,3.7l8.2-6.5L12.2,1l0.5,3.7L9.8,5.1c-3.2,0-5.7,2.6-5.7,5.8V35h3V10.9
            c0-1.5,1.3-2.8,2.8-2.8H10.2z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <polygon id="南直行" :class="Data.name === '南直行' ? '' : 'invisible'" points="14.5,35 14.5,10.1 18.2,10.1 13,0.9 7.8,10.1 11.5,10.1 11.5,35 " :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <path id="南掉头" :class="Data.name === '南掉头' ? '' : 'invisible'" d="M11.5,1.1c-3.7,0-6.7,2.6-6.7,5.8V35h3V6.8C7.8,5.3,9.5,4,11.5,4s3.7,1.3,3.7,2.8v4.3h-3.7l5.2,9.1l5.2-9h-3.7V6.9C18.2,3.7,15.2,1.1,11.5,1.1z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
          <!-- 北相位 -->
            <path id="北左转" :class="Data.name === '北左转' ? '' : 'invisible'" d="M7.1,25V1h-3v24.1c0,3.1,2.6,5.7,5.7,5.8l2.9,0.4L12.2,35l9.7-3.9l-8.2-6.5l-0.5,3.7l-3.3-0.5
              C8.4,27.8,7.1,26.6,7.1,25L7.1,25z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
            <path id="北右转" :class="Data.name === '北右转' ? '' : 'invisible'" d="M15.8,27.9l-3,0.4l-0.5-3.7l-8.2,6.5l9.7,3.9l-0.5-3.7l2.9-0.4c3.2,0,5.7-2.6,5.7-5.8V1h-3v24.1
              c0,1.5-1.3,2.8-2.8,2.8H15.8z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
            <polygon id="北直行" :class="Data.name === '北直行' ? '' : 'invisible'" points="11.5,0.9 11.5,25.9 7.8,25.9 13,35 18.2,25.9 14.5,25.9 14.5,0.9 " :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
            <path id="北掉头" :class="Data.name === '北掉头' ? '' : 'invisible'" d="M14.5,34.9c3.7,0,6.7-2.6,6.7-5.8V0.9h-3v28.3c0,1.5-1.7,2.8-3.7,2.8s-3.7-1.3-3.7-2.8v-4.3h3.7l-5.2-9.1l-5.2,9h3.7v4.3C7.8,32.3,10.8,34.9,14.5,34.9z" :fill="this.FlashColor ? this.FlashColor : (Data.color ? Data.color : defaultColor)"/>
        </svg>
      </div>
  </div>
</template>
<script>
export default {
  name: 'iconsvg',
  data () {
    return {
      defaultColor: '#fff', // 默认状态颜色
      FlashColor: undefined,
      GreenColor: '#77fb65',
      YellowColor: '#f7b500',
      lastType: '',
      clickedColor: '#299bcc',
      disabledColor: '#828282'
    }
  },
  watch: {
    Data: {
      handler: function (val) {
        if (this.isVipRoute) {
          this.handleStatus()
        }
        if (this.lastType === '') {
          if (val.type === 4 || val.type === '黄闪') {
          // 绿闪：绿-》灰-》绿 循环效果
            let highlightColor = ''
            if (val.type === 4) {
              highlightColor = this.GreenColor
            }
            if (val.type === '黄闪') {
              highlightColor = this.YellowColor
            }
            this.FlashColor = highlightColor
            this.GreenIntervalId = setInterval(() => {
              this.FlashColor = !this.FlashColor || this.FlashColor === '#828282' ? highlightColor : '#828282'
            }, 500)
            this.lastType = val.type
          }
        }
        if (this.GreenIntervalId && val.type !== 4 && val.type !== '黄闪' && val.type !== this.lastType) {
          clearInterval(this.GreenIntervalId)
          this.FlashColor = undefined
          this.lastType = ''
        }
      },
      deep: true
    }
  },
  props: {
    IconLengh: { // 相位图标长度
      type: String,
      default: '34px'
    },
    IconWdith: { // 相位图标宽度
      type: String,
      default: '22px'
    },
    IconLenghDiaoTou: {
      type: String,
      default: '18.11px'
    },
    IconWdithDiaoTou: {
      type: String,
      default: '14.47px'
    },
    Data: {
      type: Object
    },
    clickMode: {
      type: Boolean,
      default: false
    },
    isVipRoute: {
      type: Boolean,
      default: false
    }
  },
  methods: {
    handleClick (e) {
      let clickedSvg = e.currentTarget
      // console.log(clickedSvg)
      let clickedPath = clickedSvg.querySelector(`#${this.Data.name}`)
      if (clickedPath) {
        // 在这里对找到的元素进行操作
        if (clickedPath.getAttribute('disabled') === 'true') {
          return
        }
        if (clickedPath.getAttribute('fill') === this.clickedColor) {
          clickedPath.setAttribute('fill', '#fff')
          this.$emit('handleClickPhaseIcon', this.Data.key, 'cancle')
        } else {
          clickedPath.setAttribute('fill', this.clickedColor)
          this.$emit('handleClickPhaseIcon', this.Data.key, 'clicked')
        }
      }
    },
    handleStatus () {
      let IconDom = document.getElementById(this.Data.key)
      let DirDom = IconDom.querySelector(`#${this.Data.name}`)
      if (this.Data.clicked === true) {
        DirDom.setAttribute('fill', this.clickedColor)
      }
      if (this.clickMode && this.Data.disabled === true) {
        DirDom.setAttribute('fill', this.disabledColor)
        DirDom.setAttribute('disabled', 'true')
      }
      if (this.Data.disabled === undefined && this.Data.clicked === undefined) {
        DirDom.setAttribute('fill', this.defaultColor)
        DirDom.removeAttribute('disabled')
      }
    },
    IconAddEvent () {
      let IconDom = document.getElementById(this.Data.key)
      // let DirDom = IconDom.querySelector(`#${this.Data.name}`)
      // if (this.clickMode && DirDom.getAttribute('class') !== 'invisible') {
      //   DirDom.addEventListener('click', this.handleClick)
      // }
      if (this.clickMode) {
        IconDom.addEventListener('click', this.handleClick)
      }
    }
  },
  mounted () {
    if (this.isVipRoute) {
      this.handleStatus()
      this.IconAddEvent()
    }
  }
}
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
.hide {
  display: none;
}
</style>
