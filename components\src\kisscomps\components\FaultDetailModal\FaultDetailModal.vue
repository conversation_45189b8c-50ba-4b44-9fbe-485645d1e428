/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="dev-fault-detail">
    <el-dialog
      :title="$t(`openatccomponents.faultrecord.faultDetail`)"
      :visible.sync="dialogFormVisible"
      :width="isFromAtc ? '45%' : '30%'"
      :append-to-body="true"
      :close-on-click-modal="false">
    <div class="content">
      <el-table
        :data="faultList"
        style="width: 100%"
        :height="600"
        :default-sort = "{prop: 'm_unFaultOccurTime', order: 'descending'}"
       >
        <el-table-column
          v-if="isFromAtc"
          prop="m_unFaultOccurTime"
          :label="$t('openatccomponents.faultrecord.faultOccurtime')"
          sortable
          width="200">
        </el-table-column>
        <el-table-column
          v-if="isFromAtc"
          prop="m_byFaultBoardType"
          :formatter="formatterBoardType"
          :label="$t('openatccomponents.faultrecord.boardCardType')"
          sortable>
        </el-table-column>
        <el-table-column
          prop="m_wFaultType"
          :formatter="m_wFaultTypes"
          :label="$t('openatccomponents.faultrecord.faultMaintype')"
          sortable>
        </el-table-column>
        <el-table-column
          prop="m_wSubFaultType"
          :formatter="m_wSubFaultType"
          :label="$t('openatccomponents.faultrecord.faultSubtype')"
          sortable>
        </el-table-column>
        <el-table-column
          prop="m_byFaultDescValue"
          :formatter="m_byFaultDescValue"
          :label="$t('openatccomponents.faultrecord.faultValue')"
          sortable>
        </el-table-column>
        <el-table-column
          v-if="isFromAtc"
          prop="m_byFaultLevel"
          :formatter="m_byFaultLevel"
          :label="$t('openatccomponents.faultrecord.faultGrade')"
          sortable
          width="120">
        </el-table-column>
        <el-table-column
          v-if="isFromAtc"
          prop="enumerate"
          :label="$t('openatccomponents.faultrecord.enumerate')"
          align="center">
          <template slot-scope="scope">
            <el-tag v-show="scope.row.enumerate" :type="scope.row.enumerate === '1'?'info':scope.row.enumerate === '2'?'success':scope.row.enumerate === '0'?'':''">{{formatterEnumerate(scope.row)}}</el-tag>
          </template>
          </el-table-column>
        <el-table-column v-if="isFromAtc" :label="$t('openatccomponents.faultrecord.operation')" align="center">
        <template slot-scope="scope">
          <el-button type="text" @click="handleEnumerateCheck(scope.row, '2')">{{$t('openatccomponents.button.confirm')}}</el-button>
          <el-button type="text" @click="handleEnumerateCheck(scope.row, '1')">{{$t('openatccomponents.button.ignore')}}</el-button>
        </template>
        </el-table-column>
      </el-table>
    </div>
    </el-dialog>
  </div>
</template>

<script>
import {enumerateCheck, GetAllFaultRange} from '../../../api/fault.js'
import { getMessageByCode } from '../../../utils/responseMessage.js'
import { formatFaultDescValue, formatBoardType, formatEnumerate, formatSubFaultType, formatFaultLevel, formatFaultTypes } from '../../../utils/fault.js'
export default {
  name: 'fault-detail-modal',
  props: {
    childTitle: {
      type: String
    },
    agentId: {
      type: String
    },
    isFromAtc: {
      type: Boolean,
      default: true
    }
  },
  data () {
    return {
      dialogFormVisible: false,
      deviceInfo: {},
      faultList: []
    }
  },
  methods: {
    getFaultById () {
      let param = {
        agentId: this.agentId,
        isCurrentFault: true
      }
      GetAllFaultRange(param).then(res => {
        if (res.data.success !== true) {
          this.$message.error(getMessageByCode(res.data.code, this.$i18n.locale))
          return
        }
        this.faultList = res.data.data.content
      })
    },
    onViewFaultClick () {
      this.dialogFormVisible = !this.dialogFormVisible
      this.getFaultById()
    },
    formatterBoardType (row) {
      return formatBoardType(row, this.$i18n)
    },
    m_byFaultDescValue (row) {
      return formatFaultDescValue(row, this.$i18n)
    },
    m_wSubFaultType (row) {
      return formatSubFaultType(row, this.$i18n)
    },
    m_byFaultLevel (row) {
      return formatFaultLevel(row, this.$i18n)
    },
    m_wFaultTypes (row) {
      return formatFaultTypes(row, this.$i18n)
    },
    handleEnumerateCheck (row, enumerate) {
      enumerateCheck(row.agentid, row.m_wFaultID, enumerate).then(res => {
        if (!res.data.success) {
          this.$message.error(getMessageByCode(res.data.code, this.$i18n.locale))
          return
        }
        this.dialogFormVisible = false
        this.$message({
          message: this.$t('openatccomponents.common.operationsuccess'),
          type: 'success',
          duration: 1 * 1000,
          onClose: () => {
            this.getFaultById()
            this.$emit('refreshFault')
          }
        })
      })
    },
    formatterEnumerate (row) {
      return formatEnumerate(row, this.$i18n)
    }
  }
}
</script>

<style lang="scss" rel="stylesheet/scss">
.dev-update .el-dialog__body {
  padding: 30px 72px 30px 0;
}
.el-dialog__footer {
  padding: 10px 72px 38px 0;
}
</style>
