/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="lane-icon-svg">
    <div :class="laneicon.type === 'motorway' ? '' : 'hide'">
      <svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" :width="laneicon.width ? laneicon.width : iconw" :height="laneicon.height ? laneicon.height : iconh">
          <path fill-rule="evenodd"  :fill="laneicon.active === true ? highlightColor : defaultColor"
          d="M-0.002,3.940 L7.022,-0.006 L7.022,2.357 L11.085,2.357 C13.793,2.357 15.998,4.501 15.998,7.136 L15.998,16.005 L12.744,16.005 L12.744,7.136 C12.744,6.246 11.999,5.523 11.085,5.523 L7.022,5.523 L7.022,7.886 L-0.002,3.940 Z"/>
        </svg>
      </div>
      <div :class="laneicon.type === 'pedcrossing' ? '' : 'hide'">
        <svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" :width="laneicon.width ? laneicon.width : iconw" :height="laneicon.height ? laneicon.height : iconh">
          <path fill-rule="evenodd"  :fill="laneicon.active === true ? highlightColor : defaultColor"
          d="M13.473,12.000 L13.473,-0.000 L16.000,-0.000 L16.000,12.000 L13.473,12.000 ZM8.982,-0.000 L11.508,-0.000 L11.508,12.000 L8.982,12.000 L8.982,-0.000 ZM4.491,-0.000 L7.017,-0.000 L7.017,12.000 L4.491,12.000 L4.491,-0.000 ZM-0.000,-0.000 L2.526,-0.000 L2.526,12.000 L-0.000,12.000 L-0.000,-0.000 Z"/>
        </svg>
      </div>
      <div :class="laneicon.type === 'countdown' ? '' : 'hide'">
        <svg version="1.1" id="图层_1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" :width="laneicon.width ? laneicon.width : iconw" :height="laneicon.height ? laneicon.height : iconh">
          <path fill-rule="evenodd"  :fill="laneicon.active === true ? highlightColor : defaultColor"
          d="M6.561,10.851 C5.921,10.851 5.402,10.341 5.402,9.712 L5.402,6.151 C5.402,5.522 5.921,5.012 6.561,5.012 C7.201,5.012 7.719,5.522 7.719,6.151 L7.719,8.572 L10.181,8.572 C10.821,8.572 11.340,9.083 11.340,9.712 C11.340,10.341 10.821,10.851 10.181,10.851 L6.561,10.851 ZM15.113,7.994 L10.211,4.200 L11.824,3.565 C10.796,2.732 9.505,2.270 8.136,2.270 C4.927,2.270 2.317,4.838 2.317,7.994 C2.317,11.149 4.927,13.717 8.136,13.717 C10.261,13.717 12.218,12.577 13.242,10.742 C13.550,10.191 14.253,9.989 14.815,10.291 C15.376,10.594 15.581,11.287 15.273,11.839 C13.843,14.402 11.108,15.995 8.136,15.995 C3.650,15.995 0.000,12.406 0.000,7.994 C0.000,3.581 3.650,-0.008 8.136,-0.008 C10.467,-0.008 12.627,0.956 14.170,2.642 L16.003,1.921 L15.113,7.994 Z"/>
        </svg>
      </div>
      <div :class="laneicon.type === 'detector' ? '' : 'hide'">
        <svg viewBox="0 0 1024 1024" version="1.1" xmlns="http://www.w3.org/2000/svg" p-id="1551"
          xmlns:xlink="http://www.w3.org/1999/xlink" :width="laneicon.width ? laneicon.width : iconw" :height="laneicon.height ? laneicon.height : iconh">
          <path :fill="laneicon.active === true ? highlightColor : defaultColor"
            d="M864 64H160c-35.296 0-64 28.704-64 64v768c0 35.296 28.704 64 64 64h704c35.296 0 64-28.704 64-64V128c0-35.296-28.704-64-64-64zM160 896v-57.344h96a32 32 0 1 0 0-64H160v-32h96a32 32 0 1 0 0-64H160V128h704l0.032 550.656H768a32 32 0 1 0 0 64h96.064v32H768a32 32 0 1 0 0 64h96.064V896H160z"
            p-id="3622"></path>
          <path :fill="laneicon.active === true ? highlightColor : defaultColor" d="M832 160H192v480h640V160z m-64 416H256V224h512v352z" p-id="3623"></path>
        </svg>
      </div>
  </div>
</template>
<script>
export default {
  name: 'lane-icon-svg',
  data () {
    return {
      iconw: '16px',
      iconh: '16px',
      defaultColor: 'rgb(96, 98, 102)', // 默认状态颜色
      highlightColor: 'rgb(64, 158, 255)'
    }
  },
  watch: {
  },
  props: {
    laneicon: {
      type: Object
    }
  },
  methods: {},
  mounted () {}
}
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
.hide {
  display: none;
}
</style>
