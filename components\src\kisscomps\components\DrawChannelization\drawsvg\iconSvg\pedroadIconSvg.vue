/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div v-if="reset" class="pedroad-icon">
    <!-- <div class="phaseText" :style="{'left': PedData.x - PedData.w / 2  - 10 + 'px', 'top': PedData.y - PedData.h / 2 - 10  + 'px'}">{{PedData.phaselabel}}</div> -->
    <drr
      :style="{'z-index': chooseIndex === PedData.index ? 9 : 0}"
      :x="item.x"
      :y="item.y"
      :w="item.w"
      :h="item.h"
      :selected="chooseIndex === PedData.index"
      :selectable="isSeletable"
      :angle="item.angle"
      @select="handleSelectIcon(item)"
      @dragstop="boxDragStop(item, ...arguments)"
      @resizestop="boxResizeStop(item, ...arguments)"
      @rotatestop="boxRotateStop(item, ...arguments)"
    >
    <div class="phaseText"
      style="top: -28px;"
      :style="{'marginLeft': PedData.w / 2 + 'px', 'transform': `translateX(-50%) rotate(${-item.angle}deg)`}">{{PedData.phaselabel}}</div>

    <svg
      xmlns="http://www.w3.org/2000/svg"
      :viewBox="Viewbox.join(' ')"
      :width="IconW"
      :height="IconH"
    >
      <!-- 绘制模式 -->
      <g v-if="UsageMode === 'draw'" :fill="chooseIndex === PedData.index ? highlightColor : defaultColor">
        <rect v-for="(rectItem, index) in rectArr" :x="rectItem" :key="index" width="4" :height="IconH"></rect>
      </g>
      <!-- 展示模式 -->
      <g v-if="UsageMode === 'show'" :fill="this.FlashColor ? this.FlashColor : (PedData.color ? PedData.color : showDefaultColor)">
        <rect v-for="(rectItem, index) in rectArr" :x="rectItem" :key="index" width="4" :height="IconH"></rect>
      </g>
      </svg>
    </drr>
  </div>
</template>
<script>
export default {
  name: 'ped-icon-svg',
  data () {
    return {
      defaultColor: '#fff', // 默认状态颜色
      highlightColor: '#299BCC', // 选中高亮状态颜色
      item: {},
      reset: true,
      rectArr: [],
      Viewbox: [0, 0, 206, 22],
      showDefaultColor: '#fff', // 默认状态颜色
      FlashColor: undefined,
      GreenColor: '#77fb65',
      YellowColor: '#f7b500',
      lastType: ''
    }
  },
  watch: {
    // item: {
    //   handler: function (newval, oldval) {
    //     if ((JSON.stringify(oldval) !== '{}')) {
    //       // 更改原数据的位置大小数据
    //       let data = {
    //         ...this.PedData,
    //         ...newval,
    //         viewbox: this.Viewbox
    //       }
    //       let fields = Object.keys(newval).concat('viewbox')
    //       this.$emit('changePedItem', data, fields)
    //     }
    //   }
    // },
    PedData: {
      handler: function (val) {
        if (this.UsageMode === 'draw') {
          this.item.x = val.x
          this.item.y = val.y
          this.item.angle = val.angle
        }
        if (this.UsageMode === 'show') {
          if (this.lastType === '') {
            if (val.pedtype === 4 || val.pedtype === '黄闪') {
              let flashLightColor = ''
              if (val.pedtype === 4) {
                flashLightColor = this.GreenColor
              }
              if (val.pedtype === '黄闪') {
                flashLightColor = this.YellowColor
              }
              this.FlashColor = flashLightColor
              // 绿闪：绿-》灰-》绿 循环效果
              this.GreenIntervalId = setInterval(() => {
                this.FlashColor =
              !this.FlashColor || this.FlashColor === '#828282'
                ? flashLightColor
                : '#828282'
              }, 500)
              this.lastType = val.pedtype
            }
          }
          if (
            this.GreenIntervalId &&
          val.pedtype !== 4 &&
          val.pedtype !== '黄闪' &&
          val.pedtype !== this.lastType
          ) {
            clearInterval(this.GreenIntervalId)
            this.FlashColor = undefined
            this.lastType = ''
          }
        }
      },
      deep: true
    }
    // ,
    // chooseIndex: {
    //   handler: function (val) {
    //     debugger
    //   }
    // }
  },
  props: {
    PedData: {
      type: Object
    },
    isSeletable: {
      type: Boolean
    },
    chooseIndex: {
      type: Number
    },
    UsageMode: { // 当前图标模式： 绘制draw 展示show
      type: String,
      default: 'draw'
    }
  },
  methods: {
    boxDragStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
      this.handleChangeData()
    },
    boxResizeStop (origin, final) {
      this.IconW = final.w + 'px'
      this.IconH = final.h + 'px'
      this.resetSvg()
      this.drawRect(0, final.w, 8)
      this.Viewbox[2] = final.w.toFixed(1)
      this.Viewbox[3] = final.h.toFixed(1)
      this.item = JSON.parse(JSON.stringify(final))
      this.handleChangeData()
    },
    boxRotateStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
      // 第四象限的角度是负值（开源组件接口返回），转化为正值便于理解
      if (this.item.angle < 0) {
        this.item.angle = this.item.angle + 360
      }
      this.handleChangeData()
    },
    resetSvg () {
      this.reset = false
      this.$nextTick(() => {
        this.reset = true
      })
    },
    drawRect (startX, length, step) {
      this.rectArr = []
      for (let i = startX; i < length; i = i + step) {
        this.rectArr.push(i)
      }
    },
    handleSelectIcon () {
      this.$emit('handleSelectIcon', this.PedData)
    },
    handleChangeData () {
      let data = {
        ...this.PedData,
        ...this.item,
        viewbox: this.Viewbox
      }
      let fields = Object.keys(this.item).concat('viewbox')
      this.$emit('changePedItem', data, fields)
      this.handleSelectIcon()
    }
  },
  created () {
    this.IconW = this.PedData.w
    this.IconH = this.PedData.h
    this.item = {
      x: this.PedData.x,
      y: this.PedData.y,
      w: this.PedData.w,
      h: this.PedData.h,
      angle: this.PedData.angle
    }
    if (this.PedData.viewbox) {
      this.Viewbox = this.PedData.viewbox
    }
    // this.drawRect(0, 206, 8)
  },
  mounted () {
    if (this.PedData.w) {
      // 按照加载后人行道的长绘制
      this.drawRect(0, this.PedData.w, 8)
    }
  }
}
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
.hide {
  display: none;
}
.st0 {
fill: #ccc;
}
</style>
