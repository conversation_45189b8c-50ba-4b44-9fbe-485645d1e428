/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
<el-table :data="list" :max-height="tableHeight" id="footerBtn">
      <el-table-column align="center" label='No' min-width="30">
        <template slot-scope="scope">
          <span>{{scope.$index+1}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label='类型' min-width="60">
        <template slot-scope="scope">
          <span>{{scope.row.icontype}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="相位">
        <template slot-scope="scope">
            <el-select v-if="scope.row.icontype === 'vehile' || scope.row.icontype === 'ped'" v-model="scope.row.phaseid" :placeholder="$t('openatccomponents.common.select')" size="small" @change="handleChangePhase(scope.row)">
              <el-option
                v-for="item in phaseoptions"
                :key="item.id"
                :label="item.label"
                :value="item.id">
              </el-option>
            </el-select>
            <el-input v-if="scope.row.icontype === 'text'" v-model="scope.row.text" @change="handleChanngeText(scope.row)">{{scope.row.content}}</el-input>
        </template>
      </el-table-column>

      <el-table-column align="center" :label="$t('openatccomponents.overlap.operation')">
        <template slot-scope="scope">
          <el-button type="text"  @click="handleDelete(scope.row)">{{$t('openatccomponents.common.delete')}}</el-button>
        </template>
      </el-table-column>
    </el-table>
</template>

<script>
export default {
  data () {
    return {
      list: [],
      tableHeight: 400,
      phaseoptions: [{
        id: 1,
        label: 'P1'
      }, {
        id: 2,
        label: 'P2'
      }, {
        id: 3,
        label: 'P3'
      }
      ]
    }
  },
  props: {
    customlist: {
      type: Array
    }
  },
  watch: {
    customlist: {
      handler: function (val) {
        this.list = JSON.parse(JSON.stringify(val))
      },
      deep: true
    }
  },
  components: {
  },
  mounted () {
  },
  methods: {
    handleDelete (row) {
      this.$emit('deleteItem', row)
    },
    handleChanngeText (row) {
      this.$emit('changeText', row, ['text'])
    },
    handleChangePhase (row) {
      row.phaselabel = this.phaseoptions.filter(ele => ele.id === row.phaseid)[0].label
      this.$emit('changeIconDataByType', row, ['phaseid', 'phaselabel'])
    }
  }
}
</script>

<style>

</style>
