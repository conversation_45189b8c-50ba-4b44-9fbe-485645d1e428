/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="ped-svg">
    <PedEastward v-if="pedId === 1" />
    <PedWestward v-if="pedId === 2" />
    <PedSouthward v-if="pedId === 3" />
    <PedNorthward v-if="pedId === 4" />
    <PedEastTop v-if="pedId === 5" />
    <PedEastBottom v-if="pedId === 6" />
    <PedWestTop v-if="pedId === 7" />
    <PedWestBottom v-if="pedId === 8" />
    <PedSouthLeft v-if="pedId === 9" />
    <PedSouthRight v-if="pedId === 10" />
    <PedNorthLeft v-if="pedId === 11" />
    <PedNorthRight v-if="pedId === 12" />
    <PedXR v-if="pedId === 13" />
    <PedXl v-if="pedId === 14" />
    <PedNS v-if="pedId === 15" />
    <PedEW v-if="pedId === 16" />
  </div>
</template>

<script>
import PedEastward from './ped-pedeastward.vue'
import PedWestward from './ped-pedwestward.vue'
import PedNorthward from './ped-pednorthward.vue'
import PedSouthward from './ped-pedsouthward.vue'
import PedEastTop from './ped-east-top.vue'
import PedEastBottom from './ped-east-bottom'
import PedWestTop from './ped-west-top.vue'
import PedWestBottom from './ped-west-bottom.vue'
import PedSouthLeft from './ped-south-left.vue'
import PedSouthRight from './ped-south-right.vue'
import PedNorthLeft from './ped-north-left.vue'
import PedNorthRight from './ped-north-right.vue'

import PedEW from './ped-ewped.vue'
import PedNS from './ped-snped.vue'
import PedXl from './ped-xlped.vue'
import PedXR from './ped-xrped.vue'

export default {
  name: 'ped-svg',
  components: {
    PedEastward,
    PedWestward,
    PedNorthward,
    PedSouthward,
    PedEastTop,
    PedEastBottom,
    PedWestTop,
    PedWestBottom,
    PedSouthLeft,
    PedSouthRight,
    PedNorthLeft,
    PedNorthRight,
    PedEW,
    PedNS,
    PedXl,
    PedXR
  },
  props: {
    pedId: {
      type: Number
    }
  }
}
</script>
