<template>
<div>
  <div class="common-board-column">
    <div class="common-board-column-header">
      {{headerText}}
    </div>
    <div class="common-board-table-header">
      <el-row :gutter="10">
        <el-col :span="4">{{this.$t('openatccomponents.pattern.phase')}}
        </el-col>
        <el-col :span="8">{{this.$t('openatccomponents.pattern.property')}}
        </el-col>
        <el-col :span="6">{{this.$t('openatccomponents.pattern.delaystart')}}
        </el-col>
        <el-col :span="6">{{this.$t('openatccomponents.pattern.advanceend')}}
        </el-col>
      </el-row>
    </div>
     <draggable
      class="common-board-column-content"
      :list="list"
      :options="options">
      <div class="common-board-item" v-for="element in list" :key="element.id">
        <el-row :gutter="10" >
          <el-col :span="4">
            <el-tooltip class="item" effect="dark" placement="left">
              <div slot="content">{{element.name}}</div>
              <div class="common-phase-description">
                <xdrdirselector  Width="70px" Height="70px" Widths="50px" Heights="50px" :Data="styles" :showlist="element.desc" :ISActiveMask="ISActiveMask" :MaskColor="MaskColor"></xdrdirselector>
              </div>
            </el-tooltip>
        </el-col>
          <el-col :span="8">
            <el-select v-model="element.options" size="small" multiple collapse-tags :placeholder="$t('openatccomponents.common.select')">
              <el-option
                v-for="item in coordphaseOption"
                :key="item.value"
                :label="$t('openatccomponents.pattern.coordphaseOption' + item.value)"
                :value="item.value">
              </el-option>
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input-number :controls="false" size="small" :min="0" :max="255" :step="1" v-model.number="element.delaystart" ref="type"></el-input-number>
          </el-col>
          <el-col :span="6">
            <el-input-number :controls="false" size="small" :min="0" :max="255" :step="1" v-model.number="element.advanceend" ref="type"></el-input-number>
          </el-col>
        </el-row>
      </div>
    </draggable>
  </div>
</div>
</template>

<script>
import draggable from 'vuedraggable'
import xdrdirselector from '../XRDDirSelector/XRDDirSelector'
export default {
  name: 'expend-config',
  components: {
    draggable,
    xdrdirselector
  },
  data () {
    return {
      styles: {
        left: '1px',
        top: '0'
      },
      coordphaseOption: [{
        value: 1
      }, {
        value: 2
      }, {
        value: 4
      }]
    }
  },
  props: {
    headerText: {
      type: String,
      default: 'Header'
    },
    list: {
      type: Array,
      default () {
        return []
      }
    },
    ISActiveMask: {
      type: Boolean,
      default: true
    },
    // 当phase的描述为空时，显示的图形颜色。
    MaskColor: {
      type: String,
      default: '#000000'
    },
    index: {
      type: Number
    },
    options: {
      type: Object,
      default () {
        return {}
      }
    }
  }

}
</script>

<style lang="scss" scoped>
.el-input-number--small {
  width: unset !important;
}
</style>
