/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
   <el-dialog
      :title="$t('openatccomponents.plan.tip')"
      :visible.sync="dialogVisible"
      width="30%"
    >
      <input
        v-if="dialogVisible"
        type="file"
        id="importimg"
      />
      <span
        slot="footer"
        class="dialog-footer"
      >
        <el-button @click="dialogVisible = false">{{$t('openatccomponents.common.cancel')}}</el-button>
        <el-button
          type="primary"
          @click="readAsText"
        >{{$t('openatccomponents.common.confirm')}}</el-button>
      </span>
    </el-dialog>
</template>
<script>
import Svgmethods from './utils/loadutils.js'
export default {
  name: 'import-dialog',
  data () {
    return {
      dialogVisible: false
    }
  },
  methods: {
    clickOpen () {
      this.dialogVisible = true
    },
    // 底图加载
    readAsText () {
      this.svgmethods = new Svgmethods()
      this.svgmethods.clickOpen(this.loadSvgString, this.getMapWidthHeighgt)
      this.dialogVisible = false
    },
    loadSvgString (type, content) {
      if (type === 'error') {
        if (content === 'size') {
          this.$message.error(this.$t('openatccomponents.channelizationmap.importsizeerror'))
        }
        if (content === 'type') {
          this.$message.error(this.$t('openatccomponents.channelizationmap.importtypeerror'))
        }
      } else {
        this.$emit('loadSvgString', type, content)
      }
    },
    getMapWidthHeighgt (type, size) {
      this.$emit('loadMapSize', type, size)
    }
  },
  mounted () {}
}
</script>
<style scoped>
</style>
