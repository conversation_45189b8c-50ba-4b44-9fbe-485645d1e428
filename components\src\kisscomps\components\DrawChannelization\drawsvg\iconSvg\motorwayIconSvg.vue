/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div v-if="reset" class="motorway-icon">
    <!-- <div class="phaseText" :style="{'left': Data.x - Data.w / 2  - 10 + 'px', 'top': Data.y - Data.h / 2 - 10  + 'px', 'transform': 'rotate('+ Data.angle+'deg)' }">{{Data.phaselabel}}</div> -->
    <drr
      :style="{'z-index': chooseIndex === Data.index ? 9 : 0}"
      :id="'motor-'+ Data.index"
      :x="item.x"
      :y="item.y"
      :w="item.w"
      :h="item.h"
      :selected="chooseIndex === Data.index"
      :selectable="isSeletable"
      :angle="item.angle"
      :aspectRatio="true"
      @select="handleSelectIcon(item)"
      @dragstop="boxDragStop(item, ...arguments)"
      @resizestop="boxResizeStop(item, ...arguments)"
      @rotatestop="boxRotateStop(item, ...arguments)"
    >
      <div
        class="phaseText"
        style="top: -28px;"
        :style="{'marginLeft': Data.w / 2 + 'px', 'transform': `translateX(-50%) rotate(${-item.angle}deg)` }"
      >{{Data.phaselabel}}</div>

      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 27.07 89.36"
        xml:space="preserve"
        :width="IconW"
        :height="IconH"
      >
        <!-- 渠化图手动绘制图标 -->
        <g v-if="UsageMode === 'draw'">
          <g v-for="(name, index) in iconnameArr" :key="index">
            <path
              id="直行"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              :class="name === '直行' ? '' : 'invisible'"
              d="M15,36H12.35V7.7H9.09L13.65,0l4.43,7.7H15V36H12.35" />
            <path
              id="左转"
              :class="name === '左转' ? '' : 'invisible'"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M12.55,17.69V36h2.54V17.56a5,5,0,0,0-4.83-5L7.85,12.2,8.23,9,.09,12.45,7,18l.38-3.2,2.8.39a2.61,2.61,0,0,1,2.41,2.55Zm0,0V36h2.54V17.56M12.55,36h2.54m-2.54-4.28V36h2.54V31.72"
            />
            <path
              id="右转"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              :class="name === '右转' ? '' : 'invisible'"
              d="M14.61,17.69V36H12.07V17.56a5,5,0,0,1,4.83-5l2.41-.38L18.93,9l8.14,3.44L20.2,18l-.38-3.2-2.8.39a2.61,2.61,0,0,0-2.41,2.55Zm0-.13V36H12.07V17.44M14.61,36H12.07m2.54,0H12.07V31.72"
            />
            <path
              id="掉头"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              :class="name === '掉头' ? '' : 'invisible'"
              d="M12.08,36h2.54m-2.54,0h2.54m-2.54,0h2.54M3.18,24.69V28.4H0l4.45,7.68L8.9,28.4H5.72V24.69c0-1.28,1.4-2.43,3.18-2.43s3.18,1.15,3.18,2.43V36h2.54V24.69c0-2.69-2.54-5-5.72-5S3.18,22,3.18,24.69Z"
            />
            <!-- 特殊车道类型 -->
            <path
              id="sideroad"
              v-if="Data.controltype === 1"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M21,70.57l-6,0c3-2.65,5.45-7.64,4.46-10.93-1.15-3.29-4.61-6.34-11.68-7.56C11.58,52.24,28.28,53.72,21,70.57ZM8,52.34c5.73,1.48,9.54,4.41,9.39,8.39-.22,3.66-4.22,8.3-6.29,9.91-5.85,0-6.87,0-6.87,0S23,57.56,8,52.34Z"
            />
            <path
              id="bus-back"
              v-if="Data.controltype === 3 && Data.flip === true"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M8.08,82.61V80.67A3,3,0,0,0,11,77.55a3.08,3.08,0,0,0-2.87-3.11V58.22A3,3,0,0,0,11,55.1,3.08,3.08,0,0,0,8.08,52V46.15a.74.74,0,0,1,.75-.78h4.24l.5,1h3.74a2.28,2.28,0,0,1,2.25,2.33V84.69a4.18,4.18,0,0,1-4,4.67H9.7C9.33,87.94,8.58,84.43,8.08,82.61Zm9.86-32v-1.3a.73.73,0,0,0-.75-.77H10.08a.73.73,0,0,0-.75.77v1.17a.73.73,0,0,0,.75.78l7.11.13A.74.74,0,0,0,17.94,50.56Zm-.5,9.86a.67.67,0,0,0,.62-.64V53.42a.68.68,0,0,0-.62-.65H14.82a.69.69,0,0,0-.63.65v6.36a.68.68,0,0,0,.63.64Zm0,8.7a.68.68,0,0,0,.62-.65V62.11a.68.68,0,0,0-.62-.65H14.82a.69.69,0,0,0-.63.65v6.36a.69.69,0,0,0,.63.65Zm0,8.82a.68.68,0,0,0,.62-.65V70.81a.68.68,0,0,0-.62-.65H14.82a.69.69,0,0,0-.63.65v6.48a.69.69,0,0,0,.63.65Zm0,8.83a.68.68,0,0,0,.62-.65V79.76a.68.68,0,0,0-.62-.65H14.82a.69.69,0,0,0-.63.65v6.36a.69.69,0,0,0,.63.65ZM10.08,55.23a2.47,2.47,0,0,1-2.25,2.34,2.34,2.34,0,1,1,2.25-2.34Zm-3.87,0a1.76,1.76,0,0,0,1.62,1.69,1.68,1.68,0,0,0,1.62-1.69,1.53,1.53,0,0,0-1.62-1.68A1.68,1.68,0,0,0,6.21,55.23Zm3.87,22.45a2.25,2.25,0,1,1-2.25-2.33A2.29,2.29,0,0,1,10.08,77.68Zm-3.87,0a1.78,1.78,0,0,0,1.62,1.69,1.69,1.69,0,0,0,0-3.37A1.68,1.68,0,0,0,6.21,77.68Z"
            />
            <path
              id="bus-front"
              v-if="Data.controltype === 3 && Data.flip === false"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M19.43,82.59v-2a3,3,0,0,1-2.87-3.11,3.09,3.09,0,0,1,2.87-3.12V58.19a3,3,0,0,1-2.87-3.11A3.09,3.09,0,0,1,19.43,52V46.12a.73.73,0,0,0-.75-.77H14.44l-.5,1H10.2A2.28,2.28,0,0,0,8,48.72v36a4.18,4.18,0,0,0,4,4.67h5.86C18.19,87.91,18.93,84.41,19.43,82.59ZM9.58,50.54v-1.3a.73.73,0,0,1,.75-.78h7.11a.74.74,0,0,1,.75.78v1.17a.74.74,0,0,1-.75.78l-7.11.12A.73.73,0,0,1,9.58,50.54Zm.5,9.86a.69.69,0,0,1-.63-.65V53.39a.69.69,0,0,1,.63-.65H12.7a.68.68,0,0,1,.62.65v6.36a.68.68,0,0,1-.62.65Zm0,8.69a.68.68,0,0,1-.63-.65V62.09a.69.69,0,0,1,.63-.65H12.7a.68.68,0,0,1,.62.65v6.35a.67.67,0,0,1-.62.65Zm0,8.83a.69.69,0,0,1-.63-.65V70.78a.69.69,0,0,1,.63-.65H12.7a.68.68,0,0,1,.62.65v6.49a.68.68,0,0,1-.62.65Zm0,8.82a.68.68,0,0,1-.63-.64V79.74a.69.69,0,0,1,.63-.65H12.7a.68.68,0,0,1,.62.65V86.1a.68.68,0,0,1-.62.64Zm7.36-31.53a2.46,2.46,0,0,0,2.24,2.33,2.34,2.34,0,1,0-2.24-2.33Zm3.86,0a1.76,1.76,0,0,1-1.62,1.69,1.68,1.68,0,0,1-1.62-1.69,1.54,1.54,0,0,1,1.62-1.69A1.68,1.68,0,0,1,21.3,55.21ZM17.44,77.66a2.25,2.25,0,1,0,2.24-2.34A2.28,2.28,0,0,0,17.44,77.66Zm3.86,0a1.78,1.78,0,0,1-1.62,1.69,1.69,1.69,0,1,1,1.62-1.69Z"
            />
            <path
              id="brt-front"
              v-if="Data.controltype === 4 && Data.flip === false"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M8.94,49.63H19.73v2.12H8.94v3.13H6.74V46.51h2.2Zm8.25,8,2.54-1.28v2.54L16.9,60.43A18.2,18.2,0,0,0,15,61.56a1.78,1.78,0,0,0-.53.63,2.83,2.83,0,0,0-.14,1.07v.43h5.42v2.13h-13V61.34a6.2,6.2,0,0,1,.35-2.45,2.65,2.65,0,0,1,1.22-1.21,4.16,4.16,0,0,1,2.06-.48,3.85,3.85,0,0,1,2.46.73A3.19,3.19,0,0,1,14,60a5.56,5.56,0,0,1,1.08-1.13A15.89,15.89,0,0,1,17.19,57.63Zm-5.6,2a2,2,0,0,0-1-.25,1.85,1.85,0,0,0-1,.25A1.2,1.2,0,0,0,9,60.3,8,8,0,0,0,8.94,62v1.66h3.29V62.12a8.28,8.28,0,0,0-.12-1.84A1.2,1.2,0,0,0,11.59,59.64ZM14,69.23a3.49,3.49,0,0,1,2-.53,4.11,4.11,0,0,1,2.4.67A2.8,2.8,0,0,1,19.57,71a16.84,16.84,0,0,1,.16,3v3.58h-13V73.3a6.54,6.54,0,0,1,.32-2.43,2.66,2.66,0,0,1,1.13-1.18A3.46,3.46,0,0,1,10,69.21a3.45,3.45,0,0,1,1.7.41,2.76,2.76,0,0,1,1.12,1.07A2.78,2.78,0,0,1,14,69.23Zm-4.54,2.3a1.06,1.06,0,0,0-.45.61,12.39,12.39,0,0,0-.09,2V75.4h3V74a14.58,14.58,0,0,0-.05-1.56,1.32,1.32,0,0,0-.46-.85,1.56,1.56,0,0,0-1-.31A1.65,1.65,0,0,0,9.44,71.53Zm5.35-.37a1.29,1.29,0,0,0-.57.71,6.86,6.86,0,0,0-.15,1.81V75.4h3.46v-2a6.1,6.1,0,0,0-.14-1.67,1.23,1.23,0,0,0-.54-.62,1.84,1.84,0,0,0-1-.25A1.85,1.85,0,0,0,14.79,71.16Z"
            />
            <path
              id="brt-back"
              v-if="Data.controltype === 4 && Data.flip === true"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M17.88,77.55H7.09V75.43H17.88V72.3h2.19v8.37H17.88Zm-8.26-8L7.09,70.83V68.29l2.83-1.54a16.63,16.63,0,0,0,1.91-1.13,1.53,1.53,0,0,0,.53-.63,2.79,2.79,0,0,0,.15-1.07v-.43H7.09V61.36h13v4.48a6.11,6.11,0,0,1-.35,2.46,2.65,2.65,0,0,1-1.21,1.2,4.17,4.17,0,0,1-2.07.48A3.9,3.9,0,0,1,14,69.26a3.24,3.24,0,0,1-1.17-2.06,5.11,5.11,0,0,1-1.09,1.13A14.26,14.26,0,0,1,9.62,69.55Zm5.61-2a1.86,1.86,0,0,0,1,.25,1.73,1.73,0,0,0,1-.25,1.21,1.21,0,0,0,.52-.66,8.72,8.72,0,0,0,.1-1.73V63.49h-3.3v1.57a8.05,8.05,0,0,0,.13,1.84A1.16,1.16,0,0,0,15.23,67.54ZM12.84,58a3.54,3.54,0,0,1-2,.53,4.1,4.1,0,0,1-2.39-.67,2.76,2.76,0,0,1-1.21-1.6,15.86,15.86,0,0,1-.16-3V49.66h13v4.22a6.59,6.59,0,0,1-.31,2.43,2.71,2.71,0,0,1-1.13,1.18,3.71,3.71,0,0,1-3.52.07A2.64,2.64,0,0,1,14,56.49,2.88,2.88,0,0,1,12.84,58Zm4.54-2.3a1,1,0,0,0,.44-.61,12.39,12.39,0,0,0,.09-2V51.78h-3v1.4c0,.84,0,1.36,0,1.56a1.33,1.33,0,0,0,.47.85,1.53,1.53,0,0,0,1,.31A1.66,1.66,0,0,0,17.38,55.65ZM12,56a1.37,1.37,0,0,0,.56-.72,6.32,6.32,0,0,0,.16-1.81V51.78H9.29v2a6.21,6.21,0,0,0,.13,1.66A1.19,1.19,0,0,0,10,56a1.88,1.88,0,0,0,1,.25A1.94,1.94,0,0,0,12,56Z"
            />
            <path
              id="rail-back"
              v-if="Data.controltype === 5 && Data.flip === true"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M4.74,88V44.25a.81.81,0,0,1,.63-.64.69.69,0,0,1,.65.64V88a.69.69,0,0,1-.65.64A.68.68,0,0,1,4.74,88Zm2.81-1.8V47.85c-.38-1.41,2.05-2.44,3.33-2.44a8,8,0,0,1,7.92,8v4.11l.64-1c0-.13.13-.13.39-.13s.13.13.38.13l1.54,4a.78.78,0,0,1-.***********,0,0,1-.51-.13l-1.28-3.35-.77,1.29V86.17a.74.74,0,0,1-.77.77h-10A.74.74,0,0,1,7.55,86.17ZM15,53a1.1,1.1,0,0,0,1-1,2.9,2.9,0,0,0-2.94-3H9.47a.9.9,0,0,0-1,.9V52a1.1,1.1,0,0,0,1,1Zm.38,5a.69.69,0,0,0,.64-.64V55.18a.68.68,0,0,0-.64-.64H12.54v.13a.68.68,0,0,0-.64.64v2.06a.69.69,0,0,0,.64.64Zm0,3.86a.69.69,0,0,0,.64-.65v-2a.69.69,0,0,0-.64-.65H12.54a.69.69,0,0,0-.64.65v2a.69.69,0,0,0,.64.65Zm0,4a.68.68,0,0,0,.64-.64V63.15a.69.69,0,0,0-.64-.64H12.54a.68.68,0,0,0-.64.64v2.06a.69.69,0,0,0,.64.64Zm0,4.24a.68.68,0,0,0,.64-.64V67.39a.69.69,0,0,0-.64-.64H12.54a.69.69,0,0,0-.64.64v2.06a.68.68,0,0,0,.64.64Zm0,4a.69.69,0,0,0,.64-.64V71.38a.68.68,0,0,0-.64-.64H12.54a.68.68,0,0,0-.64.64v2.06a.69.69,0,0,0,.64.64Zm0,3.73a.69.69,0,0,0,.64-.64V75.24a.69.69,0,0,0-.64-.65H12.54a.52.52,0,0,0-.64.52v2.06a.69.69,0,0,0,.64.64Zm0,4.37a.69.69,0,0,0,.64-.64V79.48a.68.68,0,0,0-.64-.64H12.54a.68.68,0,0,0-.64.64v2.06a.69.69,0,0,0,.64.64Zm0,3.86a.69.69,0,0,0,.64-.65v-2a.69.69,0,0,0-.64-.65H12.54a.69.69,0,0,0-.64.65v2a.69.69,0,0,0,.64.65Z"
            />
            <path
              id="rail-front"
              v-if="Data.controltype === 5 && Data.flip === false"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M22,88V44.3a.84.84,0,0,0-.64-.64.69.69,0,0,0-.64.64V88a.69.69,0,0,0,.64.64A.69.69,0,0,0,22,88Zm-2.82-1.8V47.9c.39-1.41-2-2.44-3.32-2.44a8,8,0,0,0-7.93,8v4.12l-.64-1c0-.13-.13-.13-.38-.13s-.13.13-.39.13L5,60.5A.78.78,0,0,0,5.1,61a.77.77,0,0,0,.51-.13l1.28-3.34.76,1.28V86.22a.74.74,0,0,0,.77.77h10A.74.74,0,0,0,19.16,86.22ZM11.75,53.05a1.11,1.11,0,0,1-1-1,2.91,2.91,0,0,1,2.94-3h3.59a.91.91,0,0,1,1,.9V52a1.11,1.11,0,0,1-1,1Zm-.39,5a.69.69,0,0,1-.64-.64V55.23a.69.69,0,0,1,.64-.64h2.82v.13a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Zm0,3.86a.69.69,0,0,1-.64-.65V59.22a.7.7,0,0,1,.64-.65h2.82a.69.69,0,0,1,.64.65v2.05a.7.7,0,0,1-.64.65Zm0,4a.69.69,0,0,1-.64-.64V63.2a.69.69,0,0,1,.64-.64h2.82a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Zm0,4.25a.69.69,0,0,1-.64-.65v-2a.7.7,0,0,1,.64-.65h2.82a.69.69,0,0,1,.64.65v2a.7.7,0,0,1-.64.65Zm0,4a.69.69,0,0,1-.64-.64V71.43a.69.69,0,0,1,.64-.64h2.82a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Zm0,3.73a.69.69,0,0,1-.64-.64V75.29a.69.69,0,0,1,.64-.64h2.82a.51.51,0,0,1,.64.51v2.06a.69.69,0,0,1-.64.64Zm0,4.37a.69.69,0,0,1-.64-.64V79.53a.69.69,0,0,1,.64-.64h2.82a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Zm0,3.86a.69.69,0,0,1-.64-.64V83.39a.69.69,0,0,1,.64-.64h2.82a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Z"
            />
            <path
              id="bike-back"
              v-if="Data.controltype === 6 && Data.flip === true"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M15.38,69.56a4.48,4.48,0,0,0-.17-1.21l2.41-1.14,1.17.46v.68a.7.7,0,0,0,.**********,0,0,0,.**********,0,0,0,.72-.71v-2.9a.71.71,0,0,0-.21-.5.75.75,0,0,0-.52-.2.72.72,0,0,0-.72.7v.71L18.38,66v-6.9l.76.24c.***********.34.29v1.21a.72.72,0,0,0,.***********,0,0,0,.72-.71V59.62A1.83,1.83,0,0,0,19.59,58l-4.26-1.32a5.06,5.06,0,0,0,.05-.68,4.57,4.57,0,0,0-4.63-4.5,4.51,4.51,0,1,0,0,9A4.67,4.67,0,0,0,14.89,58l.65.2L10.3,63.33h0l0,0a.49.49,0,0,0-.***********,0,0,0,0,.07l.23.08h0l-.25,0a.76.76,0,0,0,0,.15v1.33a4.5,4.5,0,0,0,.6,9,4.57,4.57,0,0,0,4.63-4.5Zm1.55-4.15-4.77-1.9,4.77-4.66ZM11.6,66.58a3.17,3.17,0,0,1,1.68,1.11l-1.69.8Zm3,.49a4.59,4.59,0,0,0-3-1.94V64.8l4.23,1.69Zm-4.46,2.55a.7.7,0,0,0,.***********,0,0,0,.71,0L13.87,69a2.86,2.86,0,0,1,.06.58,3.19,3.19,0,0,1-6.37,0,3.11,3.11,0,0,1,2.59-3Zm.72-14.33a.7.7,0,0,0-.***********,0,0,0,.11,1.29l3.06.95a3.22,3.22,0,0,1-2.73,1.51A3.1,3.1,0,1,1,13.93,56a2.18,2.18,0,0,1,0,.25Z"
            />
            <path
              id="bike-front"
              v-if="Data.controltype === 6 && Data.flip === false"
              :fill="chooseIndex === Data.index ? highlightColor : defaultColor"
              d="M11.7,69.61a4.23,4.23,0,0,1,.17-1.22L9.44,67.25l-1.17.46v.68a.67.67,0,0,1-.**********,0,0,1-1.24-.5V65.47A.71.71,0,0,1,7,65a.77.77,0,0,1,.52-.2.71.71,0,0,1,.72.71v.7L8.68,66V59.1l-.76.23c-.14.05-.35.18-.35.29v1.22a.72.72,0,0,1-.***********,0,0,1-.72-.71V59.62A1.83,1.83,0,0,1,7.46,58l4.29-1.33A3.82,3.82,0,0,1,11.7,56a4.65,4.65,0,1,1,4.65,4.53A4.69,4.69,0,0,1,12.19,58l-.66.2,5.27,5.14h0l0,0a.75.75,0,0,1,.07.12s0,0,0,.06l-.23.09h0l.25,0a.61.61,0,0,1,0,.14v1.34a4.54,4.54,0,1,1-5.25,4.49Zm-1.56-4.17,4.79-1.91-4.79-4.68Zm5.35,1.17a3.27,3.27,0,0,0-1.69,1.13l1.7.8Zm-3,.5a4.64,4.64,0,0,1,3-2v-.33l-4.24,1.7ZM17,69.67a.7.7,0,0,1-.**********,0,0,1-.7,0L13.21,69a2.86,2.86,0,0,0-.06.58A3.2,3.2,0,1,0,17,66.55Zm-.72-14.39a.77.77,0,0,1,.56,0,.74.74,0,0,1,.***********,0,0,1-.48.89l-3.06.94a3.21,3.21,0,0,0,2.74,1.52A3.11,3.11,0,1,0,13.15,56a2.18,2.18,0,0,0,0,.25Z"
            />
          </g>
        </g>
        <!-- 展示相位图标 -->
        <g v-if="UsageMode === 'show'">
          <g v-for="(name, index) in iconnameArr" :key="index">
            <path
              id="直行"
              :class="name === '直行' ? '' : 'invisible'"
              :fill="FlashColor ? FlashColor : (Data.color ? Data.color : showDefaultColor)"
              d="M15,36H12.35V7.7H9.09L13.65,0l4.43,7.7H15V36H12.35"
            ></path>
            <path
              id="左转"
              :class="name === '左转' ? '' : 'invisible'"
              :fill="FlashColor ? FlashColor : (Data.color ? Data.color : showDefaultColor)"
              d="M12.55,17.69V36h2.54V17.56a5,5,0,0,0-4.83-5L7.85,12.2,8.23,9,.09,12.45,7,18l.38-3.2,2.8.39a2.61,2.61,0,0,1,2.41,2.55Zm0,0V36h2.54V17.56M12.55,36h2.54m-2.54-4.28V36h2.54V31.72"
            ></path>
            <path
              id="右转"
              :class="name === '右转' ? '' : 'invisible'"
              :fill="FlashColor ? FlashColor : (Data.color ? Data.color : showDefaultColor)"
              d="M14.61,17.69V36H12.07V17.56a5,5,0,0,1,4.83-5l2.41-.38L18.93,9l8.14,3.44L20.2,18l-.38-3.2-2.8.39a2.61,2.61,0,0,0-2.41,2.55Zm0-.13V36H12.07V17.44M14.61,36H12.07m2.54,0H12.07V31.72"
            ></path>
            <path
              id="掉头"
              :class="name === '掉头' ? '' : 'invisible'"
              :fill="FlashColor ? FlashColor : (Data.color ? Data.color : showDefaultColor)"
              d="M12.08,36h2.54m-2.54,0h2.54m-2.54,0h2.54M3.18,24.69V28.4H0l4.45,7.68L8.9,28.4H5.72V24.69c0-1.28,1.4-2.43,3.18-2.43s3.18,1.15,3.18,2.43V36h2.54V24.69c0-2.69-2.54-5-5.72-5S3.18,22,3.18,24.69Z"
            ></path>
          </g>
          <g>
            <!-- 特殊车道类型 -->
            <path
              v-if="Data.controltype === 3 && Data.flip === true"
              :fill="showDefaultColor"
              d="M8.08,82.61V80.67A3,3,0,0,0,11,77.55a3.08,3.08,0,0,0-2.87-3.11V58.22A3,3,0,0,0,11,55.1,3.08,3.08,0,0,0,8.08,52V46.15a.74.74,0,0,1,.75-.78h4.24l.5,1h3.74a2.28,2.28,0,0,1,2.25,2.33V84.69a4.18,4.18,0,0,1-4,4.67H9.7C9.33,87.94,8.58,84.43,8.08,82.61Zm9.86-32v-1.3a.73.73,0,0,0-.75-.77H10.08a.73.73,0,0,0-.75.77v1.17a.73.73,0,0,0,.75.78l7.11.13A.74.74,0,0,0,17.94,50.56Zm-.5,9.86a.67.67,0,0,0,.62-.64V53.42a.68.68,0,0,0-.62-.65H14.82a.69.69,0,0,0-.63.65v6.36a.68.68,0,0,0,.63.64Zm0,8.7a.68.68,0,0,0,.62-.65V62.11a.68.68,0,0,0-.62-.65H14.82a.69.69,0,0,0-.63.65v6.36a.69.69,0,0,0,.63.65Zm0,8.82a.68.68,0,0,0,.62-.65V70.81a.68.68,0,0,0-.62-.65H14.82a.69.69,0,0,0-.63.65v6.48a.69.69,0,0,0,.63.65Zm0,8.83a.68.68,0,0,0,.62-.65V79.76a.68.68,0,0,0-.62-.65H14.82a.69.69,0,0,0-.63.65v6.36a.69.69,0,0,0,.63.65ZM10.08,55.23a2.47,2.47,0,0,1-2.25,2.34,2.34,2.34,0,1,1,2.25-2.34Zm-3.87,0a1.76,1.76,0,0,0,1.62,1.69,1.68,1.68,0,0,0,1.62-1.69,1.53,1.53,0,0,0-1.62-1.68A1.68,1.68,0,0,0,6.21,55.23Zm3.87,22.45a2.25,2.25,0,1,1-2.25-2.33A2.29,2.29,0,0,1,10.08,77.68Zm-3.87,0a1.78,1.78,0,0,0,1.62,1.69,1.69,1.69,0,0,0,0-3.37A1.68,1.68,0,0,0,6.21,77.68Z"
              id="bus-back"
            ></path>
            <path
              v-if="Data.controltype === 3 && Data.flip === false"
              :fill="showDefaultColor"
              d="M19.43,82.59v-2a3,3,0,0,1-2.87-3.11,3.09,3.09,0,0,1,2.87-3.12V58.19a3,3,0,0,1-2.87-3.11A3.09,3.09,0,0,1,19.43,52V46.12a.73.73,0,0,0-.75-.77H14.44l-.5,1H10.2A2.28,2.28,0,0,0,8,48.72v36a4.18,4.18,0,0,0,4,4.67h5.86C18.19,87.91,18.93,84.41,19.43,82.59ZM9.58,50.54v-1.3a.73.73,0,0,1,.75-.78h7.11a.74.74,0,0,1,.75.78v1.17a.74.74,0,0,1-.75.78l-7.11.12A.73.73,0,0,1,9.58,50.54Zm.5,9.86a.69.69,0,0,1-.63-.65V53.39a.69.69,0,0,1,.63-.65H12.7a.68.68,0,0,1,.62.65v6.36a.68.68,0,0,1-.62.65Zm0,8.69a.68.68,0,0,1-.63-.65V62.09a.69.69,0,0,1,.63-.65H12.7a.68.68,0,0,1,.62.65v6.35a.67.67,0,0,1-.62.65Zm0,8.83a.69.69,0,0,1-.63-.65V70.78a.69.69,0,0,1,.63-.65H12.7a.68.68,0,0,1,.62.65v6.49a.68.68,0,0,1-.62.65Zm0,8.82a.68.68,0,0,1-.63-.64V79.74a.69.69,0,0,1,.63-.65H12.7a.68.68,0,0,1,.62.65V86.1a.68.68,0,0,1-.62.64Zm7.36-31.53a2.46,2.46,0,0,0,2.24,2.33,2.34,2.34,0,1,0-2.24-2.33Zm3.86,0a1.76,1.76,0,0,1-1.62,1.69,1.68,1.68,0,0,1-1.62-1.69,1.54,1.54,0,0,1,1.62-1.69A1.68,1.68,0,0,1,21.3,55.21ZM17.44,77.66a2.25,2.25,0,1,0,2.24-2.34A2.28,2.28,0,0,0,17.44,77.66Zm3.86,0a1.78,1.78,0,0,1-1.62,1.69,1.69,1.69,0,1,1,1.62-1.69Z"
              id="bus-front"
            ></path>
            <path
              v-if="Data.controltype === 4 && Data.flip === false"
              :fill="showDefaultColor"
              d="M8.94,49.63H19.73v2.12H8.94v3.13H6.74V46.51h2.2Zm8.25,8,2.54-1.28v2.54L16.9,60.43A18.2,18.2,0,0,0,15,61.56a1.78,1.78,0,0,0-.53.63,2.83,2.83,0,0,0-.14,1.07v.43h5.42v2.13h-13V61.34a6.2,6.2,0,0,1,.35-2.45,2.65,2.65,0,0,1,1.22-1.21,4.16,4.16,0,0,1,2.06-.48,3.85,3.85,0,0,1,2.46.73A3.19,3.19,0,0,1,14,60a5.56,5.56,0,0,1,1.08-1.13A15.89,15.89,0,0,1,17.19,57.63Zm-5.6,2a2,2,0,0,0-1-.25,1.85,1.85,0,0,0-1,.25A1.2,1.2,0,0,0,9,60.3,8,8,0,0,0,8.94,62v1.66h3.29V62.12a8.28,8.28,0,0,0-.12-1.84A1.2,1.2,0,0,0,11.59,59.64ZM14,69.23a3.49,3.49,0,0,1,2-.53,4.11,4.11,0,0,1,2.4.67A2.8,2.8,0,0,1,19.57,71a16.84,16.84,0,0,1,.16,3v3.58h-13V73.3a6.54,6.54,0,0,1,.32-2.43,2.66,2.66,0,0,1,1.13-1.18A3.46,3.46,0,0,1,10,69.21a3.45,3.45,0,0,1,1.7.41,2.76,2.76,0,0,1,1.12,1.07A2.78,2.78,0,0,1,14,69.23Zm-4.54,2.3a1.06,1.06,0,0,0-.45.61,12.39,12.39,0,0,0-.09,2V75.4h3V74a14.58,14.58,0,0,0-.05-1.56,1.32,1.32,0,0,0-.46-.85,1.56,1.56,0,0,0-1-.31A1.65,1.65,0,0,0,9.44,71.53Zm5.35-.37a1.29,1.29,0,0,0-.57.71,6.86,6.86,0,0,0-.15,1.81V75.4h3.46v-2a6.1,6.1,0,0,0-.14-1.67,1.23,1.23,0,0,0-.54-.62,1.84,1.84,0,0,0-1-.25A1.85,1.85,0,0,0,14.79,71.16Z"
              id="brt-front"
            ></path>
            <path
              v-if="Data.controltype === 4 && Data.flip === true"
              :fill="showDefaultColor"
              d="M17.88,77.55H7.09V75.43H17.88V72.3h2.19v8.37H17.88Zm-8.26-8L7.09,70.83V68.29l2.83-1.54a16.63,16.63,0,0,0,1.91-1.13,1.53,1.53,0,0,0,.53-.63,2.79,2.79,0,0,0,.15-1.07v-.43H7.09V61.36h13v4.48a6.11,6.11,0,0,1-.35,2.46,2.65,2.65,0,0,1-1.21,1.2,4.17,4.17,0,0,1-2.07.48A3.9,3.9,0,0,1,14,69.26a3.24,3.24,0,0,1-1.17-2.06,5.11,5.11,0,0,1-1.09,1.13A14.26,14.26,0,0,1,9.62,69.55Zm5.61-2a1.86,1.86,0,0,0,1,.25,1.73,1.73,0,0,0,1-.25,1.21,1.21,0,0,0,.52-.66,8.72,8.72,0,0,0,.1-1.73V63.49h-3.3v1.57a8.05,8.05,0,0,0,.13,1.84A1.16,1.16,0,0,0,15.23,67.54ZM12.84,58a3.54,3.54,0,0,1-2,.53,4.1,4.1,0,0,1-2.39-.67,2.76,2.76,0,0,1-1.21-1.6,15.86,15.86,0,0,1-.16-3V49.66h13v4.22a6.59,6.59,0,0,1-.31,2.43,2.71,2.71,0,0,1-1.13,1.18,3.71,3.71,0,0,1-3.52.07A2.64,2.64,0,0,1,14,56.49,2.88,2.88,0,0,1,12.84,58Zm4.54-2.3a1,1,0,0,0,.44-.61,12.39,12.39,0,0,0,.09-2V51.78h-3v1.4c0,.84,0,1.36,0,1.56a1.33,1.33,0,0,0,.47.85,1.53,1.53,0,0,0,1,.31A1.66,1.66,0,0,0,17.38,55.65ZM12,56a1.37,1.37,0,0,0,.56-.72,6.32,6.32,0,0,0,.16-1.81V51.78H9.29v2a6.21,6.21,0,0,0,.13,1.66A1.19,1.19,0,0,0,10,56a1.88,1.88,0,0,0,1,.25A1.94,1.94,0,0,0,12,56Z"
              id="brt-back"
            ></path>
            <path
              v-if="Data.controltype === 5 && Data.flip === true"
              :fill="showDefaultColor"
              d="M4.74,88V44.25a.81.81,0,0,1,.63-.64.69.69,0,0,1,.65.64V88a.69.69,0,0,1-.65.64A.68.68,0,0,1,4.74,88Zm2.81-1.8V47.85c-.38-1.41,2.05-2.44,3.33-2.44a8,8,0,0,1,7.92,8v4.11l.64-1c0-.13.13-.13.39-.13s.13.13.38.13l1.54,4a.78.78,0,0,1-.***********,0,0,1-.51-.13l-1.28-3.35-.77,1.29V86.17a.74.74,0,0,1-.77.77h-10A.74.74,0,0,1,7.55,86.17ZM15,53a1.1,1.1,0,0,0,1-1,2.9,2.9,0,0,0-2.94-3H9.47a.9.9,0,0,0-1,.9V52a1.1,1.1,0,0,0,1,1Zm.38,5a.69.69,0,0,0,.64-.64V55.18a.68.68,0,0,0-.64-.64H12.54v.13a.68.68,0,0,0-.64.64v2.06a.69.69,0,0,0,.64.64Zm0,3.86a.69.69,0,0,0,.64-.65v-2a.69.69,0,0,0-.64-.65H12.54a.69.69,0,0,0-.64.65v2a.69.69,0,0,0,.64.65Zm0,4a.68.68,0,0,0,.64-.64V63.15a.69.69,0,0,0-.64-.64H12.54a.68.68,0,0,0-.64.64v2.06a.69.69,0,0,0,.64.64Zm0,4.24a.68.68,0,0,0,.64-.64V67.39a.69.69,0,0,0-.64-.64H12.54a.69.69,0,0,0-.64.64v2.06a.68.68,0,0,0,.64.64Zm0,4a.69.69,0,0,0,.64-.64V71.38a.68.68,0,0,0-.64-.64H12.54a.68.68,0,0,0-.64.64v2.06a.69.69,0,0,0,.64.64Zm0,3.73a.69.69,0,0,0,.64-.64V75.24a.69.69,0,0,0-.64-.65H12.54a.52.52,0,0,0-.64.52v2.06a.69.69,0,0,0,.64.64Zm0,4.37a.69.69,0,0,0,.64-.64V79.48a.68.68,0,0,0-.64-.64H12.54a.68.68,0,0,0-.64.64v2.06a.69.69,0,0,0,.64.64Zm0,3.86a.69.69,0,0,0,.64-.65v-2a.69.69,0,0,0-.64-.65H12.54a.69.69,0,0,0-.64.65v2a.69.69,0,0,0,.64.65Z"
              id="rail-back"
            ></path>
            <path
              v-if="Data.controltype === 5 && Data.flip === false"
              :fill="showDefaultColor"
              d="M22,88V44.3a.84.84,0,0,0-.64-.64.69.69,0,0,0-.64.64V88a.69.69,0,0,0,.64.64A.69.69,0,0,0,22,88Zm-2.82-1.8V47.9c.39-1.41-2-2.44-3.32-2.44a8,8,0,0,0-7.93,8v4.12l-.64-1c0-.13-.13-.13-.38-.13s-.13.13-.39.13L5,60.5A.78.78,0,0,0,5.1,61a.77.77,0,0,0,.51-.13l1.28-3.34.76,1.28V86.22a.74.74,0,0,0,.77.77h10A.74.74,0,0,0,19.16,86.22ZM11.75,53.05a1.11,1.11,0,0,1-1-1,2.91,2.91,0,0,1,2.94-3h3.59a.91.91,0,0,1,1,.9V52a1.11,1.11,0,0,1-1,1Zm-.39,5a.69.69,0,0,1-.64-.64V55.23a.69.69,0,0,1,.64-.64h2.82v.13a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Zm0,3.86a.69.69,0,0,1-.64-.65V59.22a.7.7,0,0,1,.64-.65h2.82a.69.69,0,0,1,.64.65v2.05a.7.7,0,0,1-.64.65Zm0,4a.69.69,0,0,1-.64-.64V63.2a.69.69,0,0,1,.64-.64h2.82a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Zm0,4.25a.69.69,0,0,1-.64-.65v-2a.7.7,0,0,1,.64-.65h2.82a.69.69,0,0,1,.64.65v2a.7.7,0,0,1-.64.65Zm0,4a.69.69,0,0,1-.64-.64V71.43a.69.69,0,0,1,.64-.64h2.82a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Zm0,3.73a.69.69,0,0,1-.64-.64V75.29a.69.69,0,0,1,.64-.64h2.82a.51.51,0,0,1,.64.51v2.06a.69.69,0,0,1-.64.64Zm0,4.37a.69.69,0,0,1-.64-.64V79.53a.69.69,0,0,1,.64-.64h2.82a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Zm0,3.86a.69.69,0,0,1-.64-.64V83.39a.69.69,0,0,1,.64-.64h2.82a.69.69,0,0,1,.64.64v2.06a.69.69,0,0,1-.64.64Z"
              id="rail-front"
            ></path>
            <path
              v-if="Data.controltype === 6 && Data.flip === true"
              :fill="showDefaultColor"
              d="M15.38,69.56a4.48,4.48,0,0,0-.17-1.21l2.41-1.14,1.17.46v.68a.7.7,0,0,0,.**********,0,0,0,.**********,0,0,0,.72-.71v-2.9a.71.71,0,0,0-.21-.5.75.75,0,0,0-.52-.2.72.72,0,0,0-.72.7v.71L18.38,66v-6.9l.76.24c.***********.34.29v1.21a.72.72,0,0,0,.***********,0,0,0,.72-.71V59.62A1.83,1.83,0,0,0,19.59,58l-4.26-1.32a5.06,5.06,0,0,0,.05-.68,4.57,4.57,0,0,0-4.63-4.5,4.51,4.51,0,1,0,0,9A4.67,4.67,0,0,0,14.89,58l.65.2L10.3,63.33h0l0,0a.49.49,0,0,0-.***********,0,0,0,0,.07l.23.08h0l-.25,0a.76.76,0,0,0,0,.15v1.33a4.5,4.5,0,0,0,.6,9,4.57,4.57,0,0,0,4.63-4.5Zm1.55-4.15-4.77-1.9,4.77-4.66ZM11.6,66.58a3.17,3.17,0,0,1,1.68,1.11l-1.69.8Zm3,.49a4.59,4.59,0,0,0-3-1.94V64.8l4.23,1.69Zm-4.46,2.55a.7.7,0,0,0,.***********,0,0,0,.71,0L13.87,69a2.86,2.86,0,0,1,.06.58,3.19,3.19,0,0,1-6.37,0,3.11,3.11,0,0,1,2.59-3Zm.72-14.33a.7.7,0,0,0-.***********,0,0,0,.11,1.29l3.06.95a3.22,3.22,0,0,1-2.73,1.51A3.1,3.1,0,1,1,13.93,56a2.18,2.18,0,0,1,0,.25Z"
              id="bike-back"
            ></path>
            <path
              v-if="Data.controltype === 6 && Data.flip === false"
              :fill="showDefaultColor"
              d="M11.7,69.61a4.23,4.23,0,0,1,.17-1.22L9.44,67.25l-1.17.46v.68a.67.67,0,0,1-.**********,0,0,1-1.24-.5V65.47A.71.71,0,0,1,7,65a.77.77,0,0,1,.52-.2.71.71,0,0,1,.72.71v.7L8.68,66V59.1l-.76.23c-.14.05-.35.18-.35.29v1.22a.72.72,0,0,1-.***********,0,0,1-.72-.71V59.62A1.83,1.83,0,0,1,7.46,58l4.29-1.33A3.82,3.82,0,0,1,11.7,56a4.65,4.65,0,1,1,4.65,4.53A4.69,4.69,0,0,1,12.19,58l-.66.2,5.27,5.14h0l0,0a.75.75,0,0,1,.07.12s0,0,0,.06l-.23.09h0l.25,0a.61.61,0,0,1,0,.14v1.34a4.54,4.54,0,1,1-5.25,4.49Zm-1.56-4.17,4.79-1.91-4.79-4.68Zm5.35,1.17a3.27,3.27,0,0,0-1.69,1.13l1.7.8Zm-3,.5a4.64,4.64,0,0,1,3-2v-.33l-4.24,1.7ZM17,69.67a.7.7,0,0,1-.**********,0,0,1-.7,0L13.21,69a2.86,2.86,0,0,0-.06.58A3.2,3.2,0,1,0,17,66.55Zm-.72-14.39a.77.77,0,0,1,.56,0,.74.74,0,0,1,.***********,0,0,1-.48.89l-3.06.94a3.21,3.21,0,0,0,2.74,1.52A3.11,3.11,0,1,0,13.15,56a2.18,2.18,0,0,0,0,.25Z"
              id="bike-front"
            ></path>
          </g>
        </g>
      </svg>
    </drr>
  </div>
</template>
<script>
export default {
  name: 'motor-icon-svg',
  data () {
    return {
      defaultColor: '#fff', // 默认状态颜色
      highlightColor: '#299BCC', // 选中高亮状态颜色
      item: {},
      reset: true,
      iconnameArr: [],
      showDefaultColor: '#fff', // 默认状态颜色
      FlashColor: undefined,
      GreenColor: '#77fb65',
      YellowColor: '#f7b500',
      lastType: ''
    }
  },
  watch: {
    // item: {
    //   handler: function (newval, oldval) {
    //     if (JSON.stringify(oldval) !== '{}') {
    //       // 更改原数据的位置大小数据
    //       let data = {
    //         ...this.Data,
    //         ...newval
    //       }
    //       let fields = Object.keys(newval)
    //       this.$emit('changeMotorwayItem', data, fields)
    //     }
    //   }
    // },
    Data: {
      handler: function (val) {
        this.iconnameArr = val.icondirename
        if (this.UsageMode === 'draw') {
          this.item.x = val.x
          this.item.y = val.y
          this.item.angle = val.angle
        }
        if (this.UsageMode === 'show') {
          if (this.lastType === '') {
          // 绿闪：绿-》灰-》绿 循环效果
            if (val.type === 4 || val.type === '黄闪') {
              let highlightColor = ''
              if (val.type === 4) {
                highlightColor = this.GreenColor
              }
              if (val.type === '黄闪') {
                highlightColor = this.YellowColor
              }
              this.FlashColor = highlightColor
              this.GreenIntervalId = setInterval(() => {
                this.FlashColor =
              !this.FlashColor || this.FlashColor === '#828282'
                ? highlightColor
                : '#828282'
              }, 500)
              this.lastType = val.type
            }
          }
          if (
            this.GreenIntervalId &&
          val.type !== 4 &&
          val.type !== '黄闪' &&
          val.type !== this.lastType
          ) {
            clearInterval(this.GreenIntervalId)
            this.FlashColor = undefined
            this.lastType = ''
          }
        }
      },
      deep: true
    }
    // ,
    // chooseIndex: {
    //   handler: function (val) {
    //     console.log(this.Data)
    //   }
    // }
  },
  props: {
    Data: {
      type: Object
    },
    isSeletable: {
      type: Boolean
    },
    chooseIndex: {
      type: Number
    },
    UsageMode: {
      // 当前图标模式： 绘制draw 展示show
      type: String,
      default: 'draw'
    }
  },
  methods: {
    boxDragStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
      this.handleChangeData()
    },
    boxResizeStop (origin, final) {
      this.IconW = final.w + 'px'
      this.IconH = final.h + 'px'
      this.resetSvg()
      this.item = JSON.parse(JSON.stringify(final))
      this.handleChangeData()
    },
    boxRotateStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
      // 第四象限的角度是负值（开源组件接口返回），转化为正值便于理解
      if (this.item.angle < 0) {
        this.item.angle = this.item.angle + 360
      }
      this.handleChangeData()
    },
    resetSvg () {
      this.reset = false
      this.$nextTick(() => {
        this.reset = true
      })
    },
    handleSelectIcon () {
      this.$emit('handleSelectIcon', this.Data)
    },
    handleChangeData () {
      let data = {
        ...this.Data,
        ...this.item
      }
      let fields = Object.keys(this.item)
      this.$emit('changeMotorwayItem', data, fields)
      this.handleSelectIcon()
    }
  },
  created () {
    this.IconW = this.Data.w
    this.IconH = this.Data.h
    this.item = {
      x: this.Data.x,
      y: this.Data.y,
      w: this.Data.w,
      h: this.Data.h,
      angle: this.Data.angle
    }
  },
  mounted () {
    // console.log('motor', this.Data)
    this.iconnameArr = this.Data.icondirename
  }
}
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
</style>
