/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div :style="{position: 'absolute', left: Data.busleft, top: Data.bustop}" class="eastbusmap">
    <div :class="Data.id <= 4 ? '' : 'hide'">
      <svg
        version="1.1"
        id="图层_1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        x="0px"
        y="0px"
        viewBox="0 0 91 24"
        style="enable-background:new 0 0 91 24;"
        xml:space="preserve"
        :width="IconLengh"
        :height="IconWdith"
      >
        <g id="有轨电车-东" v-if="Data.controltype === 5">
          <path
            class="st0"
            d="M91,20V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4v16c0,2.2,1.8,4,4,4h83C89.2,24,91,22.2,91,20z"
          ></path>
          <g>
            <path
              class="st1"
              d="M46.7,16.4H51h0.5h0.9h0.4h0.9H59h0.9h0.4h0.9h4.9H67h0.5h0.9h4.9h0.9h0.4h0.9h0.6h0.4c0.3,0,0.6-0.2,0.6-0.6
                V8.1c0-0.3-0.2-0.6-0.6-0.6H55.2l-1-0.6l2.6-1c0.1-0.1,0.1-0.3,0.1-0.4c-0.1-0.1-0.3-0.1-0.4-0.1l-3.1,1.2c0,0.2-0.1,0.2-0.1,0.3
                c0,0.2,0,0.3,0.1,0.3l0.8,0.5H51c-3.4,0-6.2,2.8-6.2,6.2l0,0C44.8,14.8,45.6,16.7,46.7,16.4z M73.8,10.3c0-0.2,0.2-0.5,0.5-0.5
                h1.6c0.2,0,0.5,0.2,0.5,0.5v2.2c0,0.2-0.2,0.5-0.5,0.5h-1.6c-0.2,0-0.5-0.2-0.5-0.5V10.3z M70.8,10.3c0-0.2,0.2-0.5,0.5-0.5h1.6
                c0.2,0,0.5,0.2,0.5,0.5v2.2c0,0.2-0.2,0.5-0.5,0.5h-1.6c-0.2,0-0.5-0.2-0.5-0.5V10.3z M67.5,12.6v-2.2c0-0.2,0.2-0.5,0.5-0.5h1.5
                c0.2,0,0.5,0.2,0.5,0.5v2.2c0,0.2-0.2,0.5-0.5,0.5h-1.6C67.7,13.1,67.4,12.8,67.5,12.6L67.5,12.6z M64.5,10.3
                c0-0.2,0.2-0.5,0.5-0.5h1.6c0.2,0,0.5,0.2,0.5,0.5v2.2c0,0.2-0.2,0.5-0.5,0.5H65c-0.2,0-0.5-0.2-0.5-0.5V10.3z M61.4,10.3
                c0-0.2,0.2-0.5,0.5-0.5h1.6c0.2,0,0.5,0.2,0.5,0.5v2.2c0,0.2-0.2,0.5-0.5,0.5h-1.6c-0.2,0-0.5-0.2-0.5-0.5V10.3z M58.1,10.3
                c0-0.2,0.2-0.5,0.5-0.5h1.6c0.2,0,0.5,0.2,0.5,0.5v2.2c0,0.2-0.2,0.5-0.5,0.5h-1.6c-0.2,0-0.5-0.2-0.5-0.5V10.3z M55,10.3
                c0-0.2,0.2-0.5,0.5-0.5h1.6c0.2,0,0.5,0.2,0.5,0.5v2.2c0,0.2-0.2,0.5-0.5,0.5h-1.6c-0.2,0-0.5-0.2-0.5-0.5V10.3z M51.9,12.6v-2.2
                c0-0.2,0.2-0.5,0.5-0.5h1.7c0.2,0,0.5,0.2,0.5,0.5v2.2c0,0.2-0.2,0.5-0.5,0.5h-1.6c-0.2,0-0.5-0.2-0.5-0.5H51.9z M47.6,14.9v-2.8
                c0-1.3,1-2.3,2.3-2.3c0.4,0,0.8,0.4,0.8,0.8v4.3c0,0.4-0.4,0.8-0.8,0.8h-1.6C47.9,15.7,47.5,15.3,47.6,14.9L47.6,14.9z"
            ></path>
            <path
              class="st1"
              d="M78.4,18.1c0,0.2-0.2,0.5-0.5,0.5h-34c-0.2,0-0.5-0.3-0.5-0.5s0.2-0.5,0.5-0.5h34
              C78.2,17.6,78.4,17.9,78.4,18.1z"
            ></path>
          </g>
        </g>
        <g id="公交车-东" v-if="Data.controltype === 3">
          <path
            class="st0"
            d="M91,20V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4v16c0,2.2,1.8,4,4,4h83C89.2,24,91,22.2,91,20z"
          ></path>
          <g>
            <path
              class="st1"
              d="M51,14c-1,0-1.8,0.8-1.8,1.8s0.8,1.8,1.8,1.8s1.8-0.8,1.8-1.8S51.8,14,51,14z M51,17.1
              c-0.8,0-1.3-0.7-1.3-1.3c0-0.8,0.5-1.3,1.3-1.3s1.3,0.7,1.3,1.3C52.3,16.4,51.8,17,51,17.1z"
            ></path>
            <path
              class="st1"
              d="M68.3,14c-1,0-1.8,0.8-1.8,1.8s0.8,1.8,1.8,1.8s1.8-0.8,1.8-1.8S69.3,14,68.3,14z M68.3,17.1
              c-0.8,0-1.3-0.7-1.3-1.3c0-0.8,0.7-1.3,1.3-1.3c0.8,0,1.3,0.7,1.3,1.3C69.6,16.4,69,17.1,68.3,17.1z"
            ></path>
            <path
              class="st1"
              d="M73.7,6.4H46c-1,0-1.8,0.8-1.8,1.8v3l-0.8,0.4V15c0,0.3,0.2,0.6,0.6,0.6h0.2h0.9h3.4c0.1-1.3,1.1-2.3,2.4-2.3
              s2.4,1,2.4,2.3h12.5c0.1-1.3,1.1-2.3,2.4-2.3s2.4,1,2.4,2.3h1.5c1.4-0.4,4.1-1,5.2-1.3V9.6C77.3,7.8,75.8,6.3,73.7,6.4z M48,8.3
              L47.9,14c0,0.3-0.2,0.6-0.6,0.6h-0.9c-0.3,0-0.6-0.2-0.6-0.6V8.3c0-0.3,0.2-0.6,0.6-0.6h1C47.7,7.7,48,7.9,48,8.3L48,8.3z
               M55,10.2c0,0.2-0.2,0.5-0.5,0.5h-4.9c-0.2,0-0.5-0.2-0.5-0.5V8.1c0-0.2,0.2-0.5,0.5-0.5h4.9c0.2,0,0.5,0.2,0.5,0.5V10.2z
               M61.7,10.2c0,0.2-0.2,0.5-0.5,0.5h-4.9c-0.2,0-0.5-0.2-0.5-0.5V8.1c0-0.2,0.2-0.5,0.5-0.5h4.9c0.2,0,0.5,0.2,0.5,0.5V10.2z
               M68.5,10.2c0,0.2-0.2,0.5-0.5,0.5h-5c-0.2,0-0.5-0.2-0.5-0.5V8.1c0-0.2,0.2-0.5,0.5-0.5h5c0.2,0,0.5,0.2,0.5,0.5V10.2z
               M75.3,10.2c0,0.2-0.2,0.5-0.5,0.5h-4.9c-0.2,0-0.5-0.2-0.5-0.5V8.1c0-0.2,0.2-0.5,0.5-0.5h4.9c0.2,0,0.5,0.2,0.5,0.5V10.2z"
            ></path>
          </g>
        </g>
        <g id="BRT-东" v-if="Data.controltype === 4">
          <path
            class="st0"
            d="M91,20V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4v16c0,2.2,1.8,4,4,4h83C89.2,24,91,22.2,91,20z"
          ></path>
          <g>
            <path
              class="st1"
              d="M43.4,5.6h4.2c1.2,0,2,0.1,2.4,0.3c0.5,0.2,0.9,0.6,1.2,1.1s0.5,1.1,0.5,1.8c0,0.6-0.1,1.2-0.4,1.7
              s-0.6,0.9-1.1,1.1c0.6,0.2,1.1,0.6,1.5,1.1s0.5,1.2,0.5,2c0,0.9-0.2,1.7-0.7,2.4s-1,1-1.6,1.2c-0.4,0.1-1.4,0.2-3,0.2h-3.6
              L43.4,5.6L43.4,5.6z M45.5,7.7v3h1.4c0.8,0,1.4,0,1.6,0c0.4-0.1,0.6-0.2,0.9-0.5s0.3-0.6,0.3-1s-0.1-0.7-0.3-0.9
              C49.2,8,49,7.8,48.8,7.8c-0.2-0.1-0.9-0.1-2-0.1C46.8,7.7,45.5,7.7,45.5,7.7z M45.5,12.8v3.4h2c0.9,0,1.4,0,1.7-0.1
              c0.2-0.1,0.5-0.3,0.6-0.5c0.2-0.3,0.3-0.6,0.3-1s-0.1-0.8-0.3-1s-0.4-0.5-0.7-0.6s-0.9-0.2-1.8-0.2H45.5z"
            ></path>
            <path
              class="st1"
              d="M55.1,18.4V5.6h4.5c1.1,0,2,0.1,2.5,0.3s0.9,0.6,1.2,1.2s0.5,1.3,0.5,2.1c0,1-0.2,1.8-0.7,2.4
              c-0.5,0.6-1.2,1-2.1,1.2c0.5,0.3,0.8,0.7,1.1,1.1s0.7,1.1,1.2,2.1l1.3,2.5h-2.5l-1.5-2.8c-0.6-1-0.9-1.6-1.1-1.9s-0.4-0.4-0.6-0.5
              s-0.6-0.1-1.1-0.1h-0.4v5.4h-2.3V18.4z M57.2,11h1.6c1,0,1.6,0,1.8-0.1s0.5-0.3,0.6-0.5s0.2-0.6,0.2-1s-0.1-0.7-0.2-1
              S60.8,8,60.5,7.9c-0.2-0.1-0.8-0.1-1.7-0.1h-1.7V11H57.2z"
            ></path>
            <path class="st1" d="M69.2,18.4V7.7h-3.1V5.6h8.4v2.2h-3.1v10.7h-2.2V18.4z"></path>
          </g>
        </g>
        <g id="非机动车-东" v-if="Data.controltype === 6">
          <path
            class="st0"
            d="M91,20V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4v16c0,2.2,1.8,4,4,4h83C89.2,24,91,22.2,91,20z"
          ></path>
          <path
            class="st1"
            d="M62.4,10.4c-0.4,0-0.9,0.1-1.3,0.2l-1.2-2.5l0.5-1.2h0.7c0.2,0,0.4-0.1,0.5-0.2s0.2-0.3,0.2-0.5
            c0-0.4-0.3-0.7-0.7-0.7h-3c-0.2,0-0.4,0.1-0.5,0.2S57.3,6,57.3,6.2c0,0.4,0.3,0.7,0.7,0.7h0.7l-0.2,0.4h-7.2l0.2-0.8
            c0.1-0.1,0.2-0.4,0.3-0.4h1.3c0.4,0,0.7-0.3,0.7-0.7s-0.3-0.7-0.7-0.7h-1.3c-1,0-1.5,0.9-1.7,1.4l-1.4,4.4c-0.2,0-0.5-0.1-0.7-0.1
            c-2.6,0-4.7,2.1-4.7,4.7s2.1,4.7,4.7,4.7s4.7-2.1,4.7-4.7c0-1.8-1-3.4-2.6-4.2l0.2-0.7l5.4,5.3l0,0l0,0l0.1,0.1h0.1l0.1-0.2l0,0
            v0.3h0.1h1.4c0.3,2.3,2.3,4.1,4.7,4.1c2.6,0,4.7-2.1,4.7-4.7S65,10.4,62.4,10.4z M58,8.8l-2,4.9l-4.9-4.9C51.1,8.8,58,8.8,58,8.8z
             M59.2,14.2c0.2-0.7,0.6-1.3,1.2-1.7l0.8,1.7H59.2z M59.8,11.2c-1.1,0.7-1.8,1.8-2,3.1h-0.3l1.8-4.3L59.8,11.2z M62.4,15.7
            c0.3,0,0.5-0.1,0.6-0.3s0.2-0.5,0-0.7L61.7,12c0.2,0,0.4-0.1,0.6-0.1c1.8,0,3.2,1.5,3.2,3.2c0,1.8-1.5,3.2-3.2,3.2
            c-1.6,0-2.9-1.1-3.2-2.6L62.4,15.7z M47.4,15c-0.1,0.2,0,0.4,0,0.6s0.2,0.3,0.4,0.4c0.4,0.1,0.8-0.1,0.9-0.5l1-3.1
            c1,0.6,1.6,1.6,1.6,2.8c0,1.8-1.5,3.2-3.2,3.2c-1.8,0-3.2-1.5-3.2-3.2c0-1.8,1.5-3.2,3.2-3.2c0.1,0,0.2,0,0.3,0L47.4,15z"
          ></path>
        </g>
      </svg>
    </div>
  </div>
</template>
<script>
export default {
  name: 'eastBusSvg',
  props: {
    IconLengh: {
      // 相位图标长度
      type: String,
      default: '91px'
    },
    IconWdith: {
      // 相位图标宽度
      type: String,
      default: '24px'
    },
    Data: {
      type: Object
    }
  },
  methods: {},
  mounted () {}
}
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
.hide {
  display: none;
}
.st0 {
  opacity: 0.8;
  fill: #5f5f5f;
  enable-background: new;
}
.st1 {
  fill: #ffffff;
}
</style>
