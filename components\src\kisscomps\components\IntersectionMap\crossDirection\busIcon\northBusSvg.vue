/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div :style="{position: 'absolute', left: Data.busleft, top: Data.bustop}" class="northbusmap">
    <div :class="Data.id >= 9 && Data.id <= 12 ? '' : 'hide'">
      <svg
        version="1.1"
        id="图层_1"
        xmlns="http://www.w3.org/2000/svg"
        xmlns:xlink="http://www.w3.org/1999/xlink"
        x="0px"
        y="0px"
        viewBox="0 0 24 91"
        style="enable-background:new 0 0 24 91;"
        xml:space="preserve"
        :width="IconWdith"
        :height="IconLengh"
      >
        <g id="有轨电车-北" v-if="Data.controltype === 5">
          <path
            class="st0"
            d="M0,4v83c0,2.2,1.8,4,4,4h16c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4z"
          ></path>
          <g>
            <path
              class="st1"
              d="M10.1,46.2L10.1,46.2c3.4,0,6.2-2.8,6.2-6.2v-3.2l0.5,0.8c0,0.1,0.1,0.1,0.3,0.1c0.1,0,0.1-0.1,0.3-0.1
              l1.2-3.1c0-0.1,0-0.3-0.1-0.4c-0.1,0-0.3,0-0.4,0.1l-1,2.6l-0.6-1V14.5c0-0.4-0.3-0.6-0.6-0.6H8.1c-0.4,0-0.6,0.3-0.6,0.6v0.4v0.6
              v0.9v0.4v0.9v4.9v0.9V24v0.9v4.9v0.9v0.4V32v5.3v0.9v0.4v0.9V40v4.3C7.2,45.4,9.1,46.2,10.1,46.2z M11.4,17.2
              c-0.3,0-0.5-0.3-0.5-0.5v-1.6c0-0.3,0.3-0.5,0.5-0.5h2.2c0.3,0,0.5,0.3,0.5,0.5v1.6c0,0.3-0.3,0.5-0.5,0.5H11.4z M11.4,20.2
              c-0.3,0-0.5-0.3-0.5-0.5v-1.6c0-0.3,0.3-0.5,0.5-0.5h2.2c0.3,0,0.5,0.3,0.5,0.5v1.6c0,0.3-0.3,0.5-0.5,0.5H11.4z M11.4,23.5
              c-0.3,0.1-0.5-0.2-0.5-0.4v-1.6c0-0.3,0.3-0.5,0.5-0.5h2.2c0.3,0,0.5,0.3,0.5,0.5V23c0,0.3-0.3,0.5-0.5,0.5H11.4L11.4,23.5z
               M11.4,26.5c-0.3,0-0.5-0.3-0.5-0.5v-1.6c0-0.3,0.3-0.5,0.5-0.5h2.2c0.3,0,0.5,0.3,0.5,0.5V26c0,0.3-0.3,0.5-0.5,0.5H11.4z
               M11.4,29.6c-0.3,0-0.5-0.3-0.5-0.5v-1.6c0-0.3,0.3-0.5,0.5-0.5h2.2c0.3,0,0.5,0.3,0.5,0.5v1.6c0,0.3-0.3,0.5-0.5,0.5H11.4z
               M11.4,32.9c-0.3,0-0.5-0.3-0.5-0.5v-1.6c0-0.3,0.3-0.5,0.5-0.5h2.2c0.3,0,0.5,0.3,0.5,0.5v1.6c0,0.3-0.3,0.5-0.5,0.5H11.4z
               M11.4,36c-0.3,0-0.5-0.3-0.5-0.5v-1.6c0-0.3,0.3-0.5,0.5-0.5h2.2c0.3,0,0.5,0.3,0.5,0.5v1.6c0,0.3-0.3,0.5-0.5,0.5H11.4z
               M11.4,39c-0.3,0-0.5-0.3-0.5-0.5v-1.6c0-0.3,0.3-0.5,0.5-0.5h2.2c0.3,0,0.5,0.3,0.5,0.5v1.7c0,0.3-0.3,0.5-0.5,0.5h-2.2V39z
               M9,43.4c-0.4,0.1-0.8-0.3-0.8-0.7v-1.6c0-0.4,0.4-0.8,0.8-0.8h4.3c0.4,0,0.8,0.4,0.8,0.8c0,1.3-1,2.3-2.3,2.3H9L9,43.4z"
            ></path>
            <path
              class="st1"
              d="M6.3,13.1v34c0,0.3-0.3,0.5-0.5,0.5s-0.5-0.3-0.5-0.5v-34c0-0.3,0.3-0.5,0.5-0.5S6.3,12.8,6.3,13.1z"
            ></path>
          </g>
        </g>
        <g id="公交车-北" v-if="Data.controltype === 3">
          <path
            class="st0"
            d="M0,4v83c0,2.2,1.8,4,4,4h16c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4z"
          ></path>
          <g>
            <path
              class="st1"
              d="M8.2,38.2c-1,0-1.8,0.8-1.8,1.8s0.8,1.8,1.8,1.8S10,41,10,40C10,39.2,9.2,38.2,8.2,38.2z M8.2,38.7
              c0.6,0,1.3,0.5,1.3,1.3S9,41.3,8.2,41.3c-0.6,0-1.3-0.5-1.3-1.3C7,39.2,7.6,38.7,8.2,38.7z"
            ></path>
            <path
              class="st1"
              d="M8.2,20.9c-1,0-1.8,0.8-1.8,1.8s0.8,1.8,1.8,1.8s1.8-0.8,1.8-1.8S9.2,20.9,8.2,20.9z M8.2,21.4
              c0.6,0,1.3,0.5,1.3,1.3C9.5,23.3,9,24,8.2,24c-0.6,0-1.3-0.5-1.3-1.3C6.9,22,7.6,21.4,8.2,21.4z"
            ></path>
            <path
              class="st1"
              d="M14.4,13.7H9.7c-0.3,1.1-0.9,3.8-1.3,5.2v1.5c1.3,0,2.3,1.1,2.3,2.4s-1,2.3-2.3,2.4v12.5
              c1.3,0,2.3,1.1,2.3,2.4s-1,2.3-2.3,2.4v3.4v0.9V47c0,0.4,0.3,0.6,0.6,0.6h3.4l0.4-0.8h3c1,0,1.8-0.8,1.8-1.8V17.3
              C17.7,15.2,16.2,13.7,14.4,13.7z M15.7,43c0.4,0,0.6,0.3,0.6,0.6v1c0,0.4-0.3,0.6-0.6,0.6H10c-0.4,0-0.6-0.3-0.6-0.6v-0.9
              c0-0.4,0.3-0.6,0.6-0.6L15.7,43L15.7,43z M15.9,36c0.3,0,0.5,0.3,0.5,0.5v4.9c0,0.3-0.3,0.5-0.5,0.5h-2.1c-0.3,0-0.5-0.3-0.5-0.5
              v-4.9c0-0.3,0.3-0.5,0.5-0.5H15.9z M15.9,29.3c0.3,0,0.5,0.3,0.5,0.5v4.9c0,0.3-0.3,0.5-0.5,0.5h-2.1c-0.3,0-0.5-0.3-0.5-0.5v-4.9
              c0-0.3,0.3-0.5,0.5-0.5C13.8,29.3,15.9,29.3,15.9,29.3z M15.9,22.5c0.3,0,0.5,0.3,0.5,0.5v5c0,0.3-0.3,0.5-0.5,0.5h-2.1
              c-0.3,0-0.5-0.3-0.5-0.5v-5c0-0.3,0.3-0.5,0.5-0.5H15.9z M15.9,15.7c0.3,0,0.5,0.3,0.5,0.5v4.9c0,0.3-0.3,0.5-0.5,0.5h-2.1
              c-0.3,0-0.5-0.3-0.5-0.5v-4.9c0-0.3,0.3-0.5,0.5-0.5C13.8,15.7,15.9,15.7,15.9,15.7z"
            ></path>
          </g>
        </g>
        <g id="BRT-北" v-if="Data.controltype === 4">
          <path
            class="st0"
            d="M0,4v83c0,2.2,1.8,4,4,4h16c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4z"
          ></path>
          <g>
            <path
              class="st1"
              d="M18.4,16.5v4.2c0,1.2-0.1,2-0.3,2.4c-0.2,0.5-0.6,0.9-1.1,1.2c-0.5,0.3-1.1,0.5-1.8,0.5
              c-0.6,0-1.2-0.1-1.7-0.4s-0.9-0.6-1.1-1.1c-0.2,0.6-0.6,1.1-1.1,1.5s-1.2,0.5-2,0.5c-0.9,0-1.7-0.2-2.4-0.7s-1-1-1.2-1.6
              c-0.1-0.4-0.2-1.4-0.2-3v-3.6h12.9V16.5z M16.3,18.6h-3V20c0,0.8,0,1.4,0,1.6c0.1,0.4,0.2,0.6,0.5,0.9s0.6,0.3,1,0.3
              s0.7-0.1,0.9-0.3c0.2-0.2,0.4-0.4,0.4-0.6c0.1-0.2,0.1-0.9,0.1-2v-1.3H16.3z M11.2,18.6H7.7v2c0,0.9,0,1.4,0.1,1.7
              c0.1,0.2,0.3,0.5,0.5,0.6c0.3,0.2,0.6,0.3,1,0.3s0.8-0.1,1-0.3s0.5-0.4,0.6-0.7c0.1-0.3,0.2-0.9,0.2-1.8v-1.8H11.2z"
            ></path>
            <path
              class="st1"
              d="M5.6,28.3h12.9v4.5c0,1.1-0.1,2-0.3,2.5s-0.6,0.9-1.2,1.2S15.7,37,14.9,37c-1,0-1.8-0.2-2.4-0.7
              c-0.6-0.5-1-1.2-1.2-2.1c-0.3,0.5-0.7,0.8-1.1,1.1S9.1,36,8.1,36.5l-2.5,1.3v-2.5l2.8-1.5c1-0.6,1.6-0.9,1.9-1.1s0.4-0.4,0.5-0.6
              s0.1-0.6,0.1-1.1v-0.4H5.6V28.3z M13,30.4V32c0,1,0,1.6,0.1,1.8s0.3,0.5,0.5,0.6s0.6,0.2,1,0.2s0.7-0.1,1-0.2s0.4-0.4,0.5-0.7
              c0.1-0.2,0.1-0.8,0.1-1.7v-1.7H13V30.4z"
            ></path>
            <path class="st1" d="M5.6,42.4h10.7v-3.1h2.2v8.4h-2.2v-3.1H5.6V42.4z"></path>
          </g>
        </g>
        <g id="非机动车-北_1_" v-if="Data.controltype === 6">
          <path
            class="st0"
            d="M0,4v83c0,2.2,1.8,4,4,4h16c2.2,0,4-1.8,4-4V4c0-2.2-1.8-4-4-4H4C1.8,0,0,1.8,0,4z"
          ></path>
          <path
            class="st1"
            d="M8.9,23.9c-2.6,0-4.7,2.1-4.7,4.7C4.2,31,6,33,8.3,33.3v1.4c0,0.1,0,0.1,0,0.1h0.3l0,0l-0.2,0.1c0,0,0,0,0,0.1
            c0,0,0,0.1,0.1,0.1l0,0l0,0l5.3,5.4l-0.7,0.2c-0.8-1.6-2.4-2.6-4.2-2.6c-2.6,0-4.7,2.1-4.7,4.7s2.1,4.7,4.7,4.7s4.7-2.1,4.7-4.7
            c0-0.2,0-0.5-0.1-0.7l4.4-1.4c0.5-0.2,1.4-0.7,1.4-1.7v-1.3c0-0.4-0.3-0.7-0.7-0.7s-0.7,0.3-0.7,0.7V39c0,0.1-0.2,0.3-0.4,0.3
            l-0.8,0.2v-7.2l0.4-0.2v0.7c0,0.4,0.3,0.7,0.7,0.7c0.2,0,0.4-0.1,0.5-0.2s0.2-0.3,0.2-0.5v-3c0-0.4-0.3-0.7-0.7-0.7
            c-0.2,0-0.4,0.1-0.5,0.2s-0.2,0.3-0.2,0.5v0.7L15.9,31l-2.5-1.2c0.1-0.4,0.2-0.8,0.2-1.3C13.6,26,11.5,23.9,8.9,23.9z M15.2,39.9
            L10.3,35l4.9-2C15.2,33,15.2,39.9,15.2,39.9z M9.8,29.7l1.7,0.8c-0.4,0.6-1,1-1.7,1.2V29.7z M14.1,31.8l-4.3,1.8v-0.3
            c1.3-0.2,2.4-1,3.1-2L14.1,31.8z M8.3,31.8c-1.5-0.3-2.6-1.6-2.6-3.2c0-1.8,1.5-3.2,3.2-3.2c1.8,0,3.2,1.5,3.2,3.2
            c0,0.2,0,0.4-0.1,0.6l-2.7-1.3c-0.2-0.1-0.5-0.1-0.7,0s-0.3,0.4-0.3,0.6V31.8z M12.1,42.6c0,0.1,0,0.2,0,0.3c0,1.8-1.5,3.2-3.2,3.2
            c-1.8,0-3.2-1.5-3.2-3.2c0-1.8,1.5-3.2,3.2-3.2c1.1,0,2.2,0.6,2.8,1.6l-3.1,1c-0.4,0.1-0.6,0.5-0.5,0.9c0.1,0.2,0.2,0.3,0.4,0.4
            s0.4,0.1,0.6,0L12.1,42.6z"
          ></path>
        </g>
      </svg>
    </div>
  </div>
</template>
<script>
export default {
  name: 'northBusSvg',
  props: {
    IconLengh: {
      // 相位图标长度
      type: String,
      default: '91px'
    },
    IconWdith: {
      // 相位图标宽度
      type: String,
      default: '24px'
    },
    Data: {
      type: Object
    }
  },
  methods: {},
  mounted () {}
}
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
.hide {
  display: none;
}
.st0 {
  opacity: 0.8;
  fill: #5f5f5f;
  enable-background: new;
}
.st1 {
  fill: #ffffff;
}
</style>
