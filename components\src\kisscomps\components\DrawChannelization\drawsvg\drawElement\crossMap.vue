/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
<!-- 自定义底图 -->
<div class="cross-map-part" ref="crossmap">
  <!-- 底图可缩放 -->
  <drr
    id="crossmapdrr"
    :x="crossmapitem.x"
    :y="crossmapitem.y"
    :w="crossmapitem.w"
    :h="crossmapitem.h"
    :angle="crossmapitem.angle"
    :selected="chooseIndex === CrossMapData.index"
    :selectable="isSeletable"
    :aspectRatio="true"
    @select="handleSelectIcon(crossmapitem)"
    @dragstop="mapDragStop(crossmapitem, ...arguments)"
    @resizestop="mapResizeStop(crossmapitem, ...arguments)"
    @rotatestop="mapRotateStop(crossmapitem, ...arguments)"
  >
    <div v-if="mapreset && CrossMapData.type === 'vectorgraph'">
      <div class="cross-map" :style="{'width': crossmapitem.w + 'px', 'height': crossmapitem.h + 'px'}" v-html="CrossMapData.svgstr"></div>
    </div>

    <div v-if="mapreset && CrossMapData.type === 'picture'" class="cross-map" >
      <img id="pngMap" :src="CrossMapData.imgfilesrc" :style="{'width': crossmapitem.w + 'px', 'height': crossmapitem.h + 'px'}">
    </div>
  </drr>
 </div>
</template>

<script>
export default {
  data () {
    return {
      crossmapitem: {},
      mapreset: false
    }
  },
  props: {
    CrossMapData: {
      type: Object
    },
    isSeletable: {
      type: Boolean
    },
    chooseIndex: {
      type: Number
    },
    pointchange: {
      type: Boolean
    }
  },
  watch: {
    CrossMapData: {
      handler: function (val) {
        this.crossmapitem.x = val.x
        this.crossmapitem.y = val.y
        this.crossmapitem.w = val.w
        this.crossmapitem.h = val.h
        this.crossmapitem.angle = val.angle
        if (val.type === 'vectorgraph') {
          if (val.svgstr === '') {
            this.mapreset = false
            return
          }
          this.mapreset = true
        }
        if (val.type === 'picture') {
          if (val.imgfilesrc === undefined || val.imgfilesrc === '') {
            this.mapreset = false
            return
          }
          this.mapreset = true
        }
        this.$nextTick(() => {
          // 底图大小改变后，需要同步svg的宽高
          this.synchroSVGSize()
        })
      },
      deep: true
    },
    pointchange: {
      handler: function (val) {
        // 解决光标样式被选区样式覆盖问题
        let dom = document.getElementById('crossmapdrr')
        if (dom === null || dom === undefined) return
        if (val) {
          dom.style.cursor = 'url(resource/pic/icons/magnifier3.cur) 12 12,crosshair'
          return
        }
        dom.style.cursor = 'url(resource/pic/icons/magnifier3.cur) 12 12,default'
      }
    }
  },
  created () {
    this.crossmapitem = {
      x: this.CrossMapData.x,
      y: this.CrossMapData.y,
      w: this.CrossMapData.w,
      h: this.CrossMapData.h,
      angle: this.CrossMapData.angle
    }
  },
  methods: {
    mapDragStop (origin, final) {
      // 底图拖动停止
      this.crossmapitem = JSON.parse(JSON.stringify(final))
      this.handleChangeData()
    },
    synchroSVGSize () {
      let svgdoms = document.querySelectorAll('.cross-map svg')
      if (svgdoms) {
        for (let i = 0; i < svgdoms.length; i++) {
          let svgdom = svgdoms[i]
          // 解决svg源文件里有宽高属性，此时对svg父容器改变宽高，svg尺寸不会改变的问题
          // 因此要修改svg文件的宽高属性
          if (svgdom.getAttribute('width')) {
            svgdom.setAttribute('width', this.crossmapitem.w + 'px')
          }
          if (svgdom.getAttribute('height')) {
            svgdom.setAttribute('height', this.crossmapitem.h + 'px')
          }
        }
      }
    },

    mapResizeStop (origin, final) {
      // 底图改变大小停止
      this.resetMapSvg()
      this.crossmapitem = JSON.parse(JSON.stringify(final))
      this.synchroSVGSize()
      this.handleChangeData()
    },
    mapRotateStop (origin, final) {
      // 底图旋转停止
      this.crossmapitem = JSON.parse(JSON.stringify(final))// 第四象限的角度是负值（开源组件接口返回），转化为正值便于理解
      if (this.crossmapitem.angle < 0) {
        this.crossmapitem.angle = this.crossmapitem.angle + 360
      }
      this.handleChangeData()
    },
    resetMapSvg () {
      // 重绘底图svg
      this.mapreset = false
      this.$nextTick(() => {
        this.mapreset = true
      })
    },
    isEqual (obj1, obj2) {
      if (!(obj1 instanceof Object) || !(obj2 instanceof Object)) {
        return obj1 === obj2
      }
      if (Object.keys(obj1).length !== Object.keys(obj2).length) {
        return false
      }
      for (var attr in obj1) { // 逐个值进行判断
        if (obj1[attr] instanceof Object && obj2[attr] instanceof Object) {
          return this.isEqual(obj1[attr], obj2[attr])
        } else if (obj1[attr] !== obj2[attr]) {
          return false
        }
      }
      return true
    },
    handleSelectIcon (iconparams) {
      this.$emit('handleSelectIcon', this.CrossMapData, 'crossmap')
    },
    handleChangeData () {
      let data = {
        ...this.CrossMapData,
        ...this.crossmapitem
      }
      let fields = Object.keys(this.crossmapitem)
      this.$emit('changeCrossMap', data, fields)
      this.handleSelectIcon()
    }
  }
}
</script>

<style>

</style>
