/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="app-container pattern-table" ref="patternContainerRef">
    <!-- 顶部操作栏 -->
    <div class="toolbar">
      <el-button
        type="primary"
        @click="onAdd"
        :disabled="phaseList.length === 0"
      >
        添加方案
      </el-button>

      <el-radio-group
        v-model="isRing"
        style="margin-left: 30px"
        @change="handleModeChange"
      >
        <el-radio :value="false">环配置</el-radio>
        <el-radio :value="true">阶段配置</el-radio>
      </el-radio-group>

      <span class="tips">
        方案配置用于定义信号控制的时序参数
      </span>

      <div class="right-controls">
        <el-button
          type="primary"
          link
          :style="{ color: buttonTextColor }"
          @click="changeCycle"
        >
          {{ cycleChange ? '固定周期' : '可变周期' }}
        </el-button>
        <el-button
          type="primary"
          link
          @click="handleModeConvert"
        >
          转换模式
        </el-button>
      </div>
    </div>

    <!-- 主表格 -->
    <el-table
      :data="patternList"
      :max-height="tableHeight"
      highlight-current-row
      row-key="id"
      @expand-change="handleExpandChange"
      ref="patternTableRef"
    >
      <!-- 展开行 -->
      <el-table-column type="expand">
        <template #default="{ row, $index }">
          <div class="expand-content">
            <el-tabs
              v-model="activeTabList[$index]"
              type="card"
            >
              <!-- 环配置标签页 -->
              <el-tab-pane
                v-if="!isRing"
                label="环配置"
                name="ring"
              >
                <div class="ring-config">
                  <div v-for="(ring, ringIndex) in row.rings" :key="ringIndex" class="ring-item">
                    <h4>环 {{ ringIndex + 1 }}</h4>
                    <div v-if="ring && ring.length > 0" class="ring-phases">
                      <div v-for="(phase, phaseIndex) in ring" :key="phaseIndex" class="phase-item">
                        <el-card>
                          <div class="phase-header">
                            <span>相位 {{ phase.id }}</span>
                            <span>{{ phase.name || `相位${phase.id}` }}</span>
                          </div>
                          <div class="phase-controls">
                            <el-form-item label="绿灯时间">
                              <el-input-number
                                v-model="phase.value"
                                :min="1"
                                :max="999"
                                size="small"
                                @change="updateRingCycle(row)"
                              />
                            </el-form-item>
                            <el-form-item label="模式">
                              <el-select v-model="phase.mode" size="small">
                                <el-option label="正常" :value="2" />
                                <el-option label="忽略" :value="7" />
                                <el-option label="黄闪" :value="8" />
                                <el-option label="全红" :value="9" />
                              </el-select>
                            </el-form-item>
                          </div>
                        </el-card>
                      </div>
                    </div>
                    <div v-else class="empty-ring">
                      <el-empty description="该环暂无相位配置" />
                    </div>
                  </div>
                </div>
              </el-tab-pane>

              <!-- 阶段配置标签页 -->
              <el-tab-pane
                v-if="isRing"
                label="阶段配置"
                name="stage"
              >
                <div class="stage-config">
                  <div v-for="(stage, stageIndex) in row.stagesList" :key="stageIndex" class="stage-item">
                    <el-card>
                      <div class="stage-header">
                        <span>阶段 {{ stageIndex + 1 }}</span>
                        <el-button
                          type="danger"
                          link
                          @click="deleteStage(row.stagesList, stageIndex)"
                          v-if="row.stagesList.length > 1"
                        >
                          删除
                        </el-button>
                      </div>
                      <div class="stage-controls">
                        <el-form-item label="绿灯时间">
                          <el-input-number
                            v-model="stage.green"
                            :min="0"
                            :max="999"
                            size="small"
                            @change="updateStageCycle(row)"
                          />
                        </el-form-item>
                        <el-form-item label="黄灯时间">
                          <el-input-number
                            v-model="stage.yellow"
                            :min="0"
                            :max="10"
                            size="small"
                            @change="updateStageCycle(row)"
                          />
                        </el-form-item>
                        <el-form-item label="红灯时间">
                          <el-input-number
                            v-model="stage.red"
                            :min="0"
                            :max="10"
                            size="small"
                            @change="updateStageCycle(row)"
                          />
                        </el-form-item>
                        <el-form-item label="相位选择">
                          <el-select
                            v-model="stage.phases"
                            multiple
                            placeholder="选择相位"
                            size="small"
                          >
                            <el-option
                              v-for="phase in phaseList"
                              :key="phase.id"
                              :label="`相位${phase.id}`"
                              :value="phase.id"
                            />
                          </el-select>
                        </el-form-item>
                      </div>
                    </el-card>
                  </div>
                  <div class="add-stage">
                    <el-button
                      type="primary"
                      @click="addStage(row.stagesList)"
                    >
                      添加阶段
                    </el-button>
                  </div>
                </div>
              </el-tab-pane>
            </el-tabs>
          </div>
        </template>
      </el-table-column>

      <!-- ID列 -->
      <el-table-column
        prop="id"
        label="ID"
        width="80"
        align="center"
        sortable
      />

      <!-- 描述列 -->
      <el-table-column
        label="描述"
        width="190"
        align="center"
      >
        <template #default="{ row }">
          <el-input
            v-model="row.desc"
            size="small"
            @blur="handleDescChange(row)"
          />
        </template>
      </el-table-column>

      <!-- 相位差列 -->
      <el-table-column
        label="相位差"
        width="80"
        align="center"
      >
        <template #default="{ row }">
          <el-input-number
            v-model="row.offset"
            size="small"
            :min="0"
            :max="row.cycle || 999"
            @change="handleOffsetChange(row)"
          />
        </template>
      </el-table-column>

      <!-- 周期列 -->
      <el-table-column
        prop="cycle"
        label="周期"
        width="80"
        align="center"
      />

      <!-- 方案图列 -->
      <el-table-column
        label="方案图"
        align="center"
      >
        <template #default="{ row }">
          <div class="pattern-diagram">
            <div v-if="isRing && row.stagesList?.length > 0" class="stage-diagram">
              <div class="stage-bar">
                <div
                  v-for="(stage, index) in row.stagesList"
                  :key="index"
                  class="stage-segment"
                  :style="{
                    width: `${(stage.stageSplit / row.cycle) * 100}%`,
                    backgroundColor: getStageColor(index)
                  }"
                >
                  {{ stage.stageSplit }}s
                </div>
              </div>
            </div>
            <div v-else-if="!isRing && row.rings?.length > 0" class="ring-diagram">
              <div class="ring-bar">
                <div
                  v-for="(ring, ringIndex) in row.rings"
                  :key="ringIndex"
                  v-if="ring && ring.length > 0"
                  class="ring-segment"
                >
                  环{{ ringIndex + 1 }}: {{ getRingCycle(ring) }}s
                </div>
              </div>
            </div>
            <div v-else class="empty-diagram">
              <el-empty description="暂无配置" :image-size="60" />
            </div>
          </div>
        </template>
      </el-table-column>

      <!-- 操作列 -->
      <el-table-column
        label="操作"
        width="200"
        align="center"
      >
        <template #default="{ row, $index }">
          <el-button
            type="primary"
            link
            @click="handleOptimize($index, row)"
          >
            优化
          </el-button>
          <el-button
            type="primary"
            link
            @click="handleClone($index, row)"
          >
            克隆
          </el-button>
          <el-button
            type="danger"
            link
            @click="handleDelete($index, row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 优化对话框 -->
    <el-dialog
      v-model="optimizeDialogVisible"
      title="方案优化"
      width="60%"
      append-to-body
    >
      <div class="optimize-content">
        <el-form label-width="120px">
          <el-form-item label="优化目标">
            <el-radio-group v-model="optimizeTarget">
              <el-radio value="cycle">最小周期</el-radio>
              <el-radio value="delay">最小延误</el-radio>
              <el-radio value="capacity">最大通行能力</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="约束条件">
            <el-checkbox-group v-model="optimizeConstraints">
              <el-checkbox value="minGreen">最小绿灯时间</el-checkbox>
              <el-checkbox value="maxCycle">最大周期限制</el-checkbox>
              <el-checkbox value="phaseSequence">相位顺序</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="optimizeDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="executeOptimize">开始优化</el-button>
      </template>
    </el-dialog>

    <!-- 模式转换确认对话框 -->
    <el-dialog
      v-model="convertDialogVisible"
      title="模式转换确认"
      width="400px"
    >
      <p>确认要转换配置模式吗？这将重新计算所有方案的配置参数。</p>
      <template #footer>
        <el-button @click="convertDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmModeConvert">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>
    <el-table
      :data="patternList"
      :max-height="tableHeight"
      highlight-current-row
      :row-key="getRowKeys"
      :expand-row-keys="expands"
      @expand-change="expandChange"
      ref="singleTable"
      id="footerBtn"
    >
      <el-table-column type="expand">
        <template slot-scope="scope">
          <el-tabs
            v-model="activeList[scope.$index]"
            @tab-click="handleClick"
            type="card"
          >
            <el-tab-pane
              style="float: left"
              v-if="!isRing"
              :label="$t('edge.pattern.ringConfig')"
              name="ring"
            >
              <div class="components-container board">
                <Kanban
                  v-for="n in ringCount"
                  :key="n"
                  class="kanban todo"
                  :list="scope.row.rings[n - 1]"
                  :options="scope.row.options"
                  :header-text="$t('edge.pattern.ring') + n"
                  :index="scope.$index"
                  @handleMode="handleMode"
                  @handleSplit="handleSplit"
                />
              </div>
            </el-tab-pane>
            <el-tab-pane v-if="isRing" :label="$t('edge.pattern.stageConfig')" name="kanban">
              <!-- <el-scrollbar :vertical="true"> -->
              <div class="stage-panel-contener">
                <StageKanban
                  v-for="(stage, index) in scope.row.stagesList"
                  class="kanban todo stagekanban"
                  :key="index"
                  :stage="stage"
                  :atctype="atctype"
                  :stageInfo="scope.row.stagesList"
                  :isRing="isRing"
                  :stageData="stageparamList"
                  :options="scope.row.options"
                  :coordphaseOption="coordphaseOption"
                  :rowIndex="scope.$index"
                  :subIndex="index"
                  @onStageSplitChange="onStageSplitChange"
                  @stageSplitChange="stageSplitChange"
                  @onStageDelaystartChange="onStageDelaystartChange"
                  @onStageAdvanceendChange="onStageAdvanceendChange"
                />
                <!-- :header-text="$t('edge.pattern.stage') + Number(index + 1)" -->
                <div v-if="isRing" style="margin-left: 20px">
                  <el-button
                    type="primary"
                    @click="addStage(scope.row.stagesList)"
                    icon="el-icon-plus"
                    circle
                  ></el-button>
                </div>
              </div>
              <!-- </el-scrollbar> -->
            </el-tab-pane>
            <el-tab-pane
              style="float: left"
              v-if="!isRing"
              :label="$t('edge.pattern.parameters')"
              name="parame"
            >
              <!-- <el-row :gutter="20">
                <el-col :span="12"> -->
              <div class="components-container board">
                <ExpendConfig
                  class="kanban todo"
                  v-for="(j, index) in ringCounts"
                  :key="index"
                  :header-text="$t('edge.pattern.ring') + j"
                  :list="scope.row.rings[j - 1]"
                  :options="scope.row.options"
                />
                <div
                  v-if="scope.row.forbiddenstage"
                  class="stage-item"
                  style="margin: 0 40px"
                >
                  <el-row v-if="scope.row.forbiddenstage">
                    <el-col :span="8">
                      {{ $t('edge.pattern.forbiddenstage') }}
                      <el-input
                        class="stage-value"
                        size="small"
                        v-model="scope.row.forbiddenstage"
                      ></el-input>
                    </el-col>
                  </el-row>
                  <el-row v-if="scope.row.screenstage">
                    <el-col :span="8">
                      {{ $t('edge.pattern.screenstage') }}
                      <el-input
                        class="stage-value"
                        size="small"
                        v-model="scope.row.screenstage"
                      ></el-input>
                    </el-col>
                  </el-row>
                  <el-row v-if="scope.row.coordinatestage">
                    <el-col :span="8">
                      {{ $t('edge.pattern.coordinatestage') }}
                      <el-input
                        class="stage-value"
                        size="small"
                        v-model="scope.row.coordinatestage"
                      ></el-input>
                    </el-col>
                  </el-row>
                </div>
              </div>
            </el-tab-pane>
            <el-tab-pane
              v-if="!isRing"
              :label="$t('edge.pattern.other')"
              name="overlap"
              >
              <el-row :gutter="20">
                <el-col :span="24">
                  <div class="special-params">
                    <el-table :cell-style="{ padding: '1px' }" :data="scope.row.patternoverlaplist" highlight-current-row :max-height="tableHeight">
                        <el-table-column align="center" label='ID' min-width="30">
                          <template slot-scope="scope">
                            <span>{{scope.row.id}}</span>
                          </template>
                        </el-table-column>
                        <el-table-column align="center" :label="$t('edge.overlap.earlyred')">
                          <template slot-scope="scope">
                            <el-input-number size="small" controls-position="right" :min="0" :max="65535" :step="1" :precision="0" v-model.number="scope.row.leadinggreen"></el-input-number>
                            <!-- <span>{{scope.row.leadinggreen}}</span> -->
                          </template>
                        </el-table-column>
                        <el-table-column align="center" :label="$t('edge.overlap.lategreen')">
                          <template slot-scope="scope">
                            <el-input-number size="small" controls-position="right" :min="0" :max="65535" :step="1" :precision="0" v-model.number="scope.row.lategreen"></el-input-number>
                            <!-- <span>{{scope.row.lategreen}}</span> -->
                          </template>
                        </el-table-column>
                      </el-table>
                  </div>
                  <div>
                    <span style="margin-left: 20PX;margin-right: 10PX;">{{$t('edge.pattern.specialPatternConfig')}}</span>
                    <el-select multiple v-model="scope.row.special" clearable :placeholder="$t('edge.common.select')" style="width:320PX;">
                      <el-option
                          v-for="item in configOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                          >
                      </el-option>
                    </el-select>
                    <over-lap
                      :stageList="scope.row.overlapList"
                      :checked="true"
                      :overlap="overlap"
                      :cycleChange="cycleChange"
                      :cycle="scope.row.overlapCycle"
                    >
                    </over-lap>
                  </div>
                </el-col>
              </el-row>
            </el-tab-pane>
            <!-- <el-tab-pane
              style="float: left"
              v-if="!isRing"
              :label="$t('edge.pattern.patternOptimize')"
              name="patternOptimize"
            >
              <div class="components-container board optimize">
                <PatternOptimize
                  v-for="n in optimizes"
                  :key="n"
                  class="kanban todo"
                  :list="scope.row.rings[n - 1]"
                  :id="scope.row.id"
                  :rings="scope.row.rings"
                  :phaseList="phaseList"
                  :options="scope.row.options"
                  :header-text="$t('edge.pattern.ring') + n"
                  :index="scope.$index"
                  :nowNumber="n"
                  @handleSplit="handleSplit"
                />
              </div>
            </el-tab-pane> -->
          </el-tabs>
        </template>
      </el-table-column>
      <el-table-column sortable prop="id" align="center" width="80px" label="ID">
        <template slot-scope="scope">
          <span>{{ scope.row.id }}</span>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="190px"
        :label="$t('edge.pattern.desc')"
        prop="desc"
      >
        <template slot-scope="scope">
          <el-input size="small" v-model="scope.row.desc"></el-input>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="80px"
        :label="$t('edge.pattern.offset')"
        prop="offset"
      >
        <template slot-scope="scope">
          <el-input
            size="small"
            v-model.number="scope.row.offset"
            @blur="checkOffset(scope.row.offset, scope.row)"
          ></el-input>
        </template>
      </el-table-column>
      <el-table-column
        align="center"
        width="60px"
        :label="$t('edge.pattern.cycle')"
        prop="cycle"
      >
      </el-table-column>
      <el-table-column
        align="center"
        :label="$t('edge.pattern.plan')"
        prop="plan"
      >
        <template slot-scope="scope">
          <div class="pattern-figure">
            <pattern-list v-if="scope.row.rings.length > 0 || isRing"
              :patternId="scope.row.id"
              :contrloType="scope.row.contrloType"
              :index="scope.$index"
              :isMove="false"
              :cycleChange="cycleChange"
              :stagesChange="scope.row.stagesList"
              :patternStatusList="scope.row.rings"
              :patternList="patternList"
              :cycles="scope.row.cycle"
              :phaseList="phaseList"
              :agentId="agentId"
              :isBorder="true"
              @handleSplitMove="handleSplitMove"
              :showBarrier="true">
            </pattern-list>
            <!-- <BoardCard
              v-if="scope.row.rings.length > 0 || isRing"
              :patternId="scope.row.id"
              :isMove="false"
              :contrloType="scope.row.contrloType"
              :stagesChange="scope.row.stagesList"
              :patternStatusList="scope.row.rings"
              :index="scope.$index"
              :cycleChange="cycleChange"
              :patternList="patternList"
              :cycles="scope.row.cycle"
              :phaseList="phaseList"
              :agentId="agentId"
              :showBarrier="true"
            ></BoardCard> -->
          </div>
        </template>
      </el-table-column>
      <el-table-column
        width="140px"
        :label="$t('edge.pattern.operation')"
      >
        <template slot-scope="scope">
          <el-button
            type="text"
            @click="handleOptimize(scope.$index, scope.row)"
            >{{ $t('edge.route.optimize') }}</el-button
          >
          <el-button
            type="text"
            @click="handleDelete(scope.$index, scope.row)"
            >{{ $t('edge.common.delete') }}</el-button
          >
          <el-button
            type="text"
            v-if="isRing"
            @click="handleClone(scope.$index, scope.row)"
            >{{ $t('edge.common.clone') }}</el-button
          >
          <el-dialog
            class="dialog-form"
            append-to-body
            v-if="selectedRow !== null && selectedRow.contrloType !== 'stage'"
            :title="$t('edge.pattern.patternOptimize')"
            :visible.sync="dialogFormVisible">
            <div class="">
              <!-- <PatternOptimize
                v-for="n in optimizes"
                :key="n"
                class="kanban todo"
                :list="selectedRow.rings[n - 1]"
                :id="selectedRow.id"
                :rings="selectedRow"
                :phaseList="phaseList"
                :options="selectedRow.options"
                :header-text="$t('edge.pattern.ring') + n"
                :nowNumber="n"
                @optimizesucess="optimizesucess"
              ></PatternOptimize> -->
              <!-- :list="selectedRow.rings" -->
              <pattern-optimize
                ref="patternOptimize"
                :tableRing="tableRing"
                :id="selectedRow.id"
                :rings="selectedRow"
                :showStyle="showStyle"
                :styles="styles"
                :phaseList="phaseList"
                @optimizesucess="optimizesucess"
              ></pattern-optimize>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button
                type="primary"
                @click="optimize"
              >{{$t('edge.route.optimize')}}</el-button>
              <el-button @click="cancelOptimize">{{$t('edge.common.cancel')}}</el-button>
            </span>
          </el-dialog>
          <el-dialog
          width="60%"
          v-if="selectedRow !== null && selectedRow.contrloType === 'stage'"
            :title="$t('edge.pattern.patternOptimize')"
            :visible.sync="dialogFormVisible">
            <!-- :stageData="stagetransform" -->
            <div class="">
              <stage-optimize
                ref="stageOptimize"
                :tableRing="tableRing"
                :id="selectedRow.id"
                :rings="selectedRow"
                :stagesChange="selectedRow.stagesList"
                :phaseList="phaseList"
                :showStyle="showStyle"
                :styles="styles"
                @optimizestage="optimizestage"
              ></stage-optimize>
            </div>
            <span slot="footer" class="dialog-footer">
              <el-button
                type="primary"
                @click="stageOptimizes"
              >{{$t('edge.route.optimize')}}</el-button>
              <el-button @click="cancelOptimize">{{$t('edge.common.cancel')}}</el-button>
            </span>
          </el-dialog>
        </template>
      </el-table-column>
    </el-table>
     <Messagebox :visible="messageboxVisible" :text="$t('edge.common.changestage')"  @cancle="cancle" @ok="ok"/>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, nextTick } from 'vue'
import { useStore } from 'vuex'
import { ElMessage, ElMessageBox } from 'element-plus'

// 类型定义
interface PhaseItem {
  id: number
  desc: string
  ring: number
  direction: number[]
  peddirection?: number[]
}

interface RingItem {
  id: number
  name: string
  desc: any[]
  value: number
  mode: number
  options: number[]
  flowperhour: number
  saturation: number
  delaystart: number
  advanceend: number
}

interface StageItem {
  key: number
  stageNo: number
  green: number
  yellow: number
  red: number
  min: number
  max: number
  phases: number[]
  stageSplit: number
  delaystart: number
  advanceend: number
}

interface PatternItem {
  id: number
  desc: string
  offset: number
  cycle: number
  rings: RingItem[][]
  stagesList: StageItem[]
  contrloType: 'ring' | 'stage'
  special?: string[]
  patternoverlaplist?: any[]
  overlapList?: any[]
  overlapCycle?: number
  stages?: number[][]
}

// 组合式API
const store = useStore()

// 响应式数据
const patternContainerRef = ref()
const patternTableRef = ref()
const patternOptimizeRef = ref()
const stageOptimizeRef = ref()

const tableHeight = ref(760)
const isRing = ref(false)
const cycleChange = ref(true)
const buttonTextColor = ref('#299BCC')
const expandedRows = ref<number[]>([])
const activeTabList = ref<string[]>([])
const optimizeDialogVisible = ref(false)
const convertDialogVisible = ref(false)
const selectedPattern = ref<PatternItem | null>(null)
const id = ref(1)

// 全局参数模型
let globalParamModel: any = null

// 计算属性
const patternList = computed(() => store.state.globalParam.tscParam.patternList)
const phaseList = computed(() => store.state.globalParam.tscParam.phaseList || [])

// 环数量计算
const ringCount = computed(() => {
  if (phaseList.value.length === 0) return []
  const rings = phaseList.value.map((phase: PhaseItem) => phase.ring)
  const uniqueRings = Array.from(new Set(rings)) as number[]
  return uniqueRings.sort((a, b) => a - b)
})

// 监听器
watch(isRing, (newValue) => {
  // 保存配置模式到本地存储
  localStorage.setItem('patternMode', newValue ? 'stage' : 'ring')
  // 重新初始化活动标签页
  activeTabList.value = patternList.value.map(() => newValue ? 'stage' : 'ring')
})

watch(patternList, () => {
  if (patternList.value.length > 0) {
    initializeData()
  }
}, { deep: true })

// 设置表格高度
const setTableHeight = () => {
  nextTick(() => {
    if (patternContainerRef.value) {
      tableHeight.value = patternContainerRef.value.offsetHeight - 120
      window.onresize = () => {
        if (patternContainerRef.value) {
          tableHeight.value = patternContainerRef.value.offsetHeight - 120
        }
      }
    }
  })
}

// 初始化数据
const initializeData = () => {
  increaseId()
  // 初始化活动标签页
  activeTabList.value = patternList.value.map(() => isRing.value ? 'stage' : 'ring')
}

// 增加ID（寻找最小可用ID）
const increaseId = () => {
  const patternIdList = patternList.value.map((pattern: PatternItem) => pattern.id)
  if (patternIdList.length > 0) {
    for (let j = 1; j <= 108; j++) {
      if (!patternIdList.includes(j)) {
        id.value = j
        return
      }
    }
  } else {
    id.value = 1
  }
}

// 添加缺失的响应式数据
const optimizeTarget = ref('cycle')
const optimizeConstraints = ref<string[]>([])

// 模板中使用的方法
// 更新环周期
const updateRingCycle = (pattern: PatternItem) => {
  pattern.cycle = calculateRingCycle(pattern.rings)
}

// 更新阶段周期
const updateStageCycle = (pattern: PatternItem) => {
  const stageCycle = pattern.stagesList.reduce((total: number, stage: StageItem) => {
    return total + (stage.stageSplit || stage.green + stage.yellow + stage.red || 0)
  }, 0)
  pattern.cycle = stageCycle
}

// 删除阶段
const deleteStage = (stagesList: StageItem[], stageIndex: number) => {
  if (stagesList.length <= 1) {
    ElMessage.error('至少需要保留一个阶段！')
    return
  }
  stagesList.splice(stageIndex, 1)
}

// 获取阶段颜色
const getStageColor = (index: number): string => {
  const colors = ['#67C23A', '#E6A23C', '#F56C6C', '#909399', '#409EFF']
  return colors[index % colors.length]
}

// 获取环周期
const getRingCycle = (ring: RingItem[]): number => {
  return ring.reduce((total, phase) => {
    return phase.mode === 7 ? total : total + phase.value
  }, 0)
}

// 主要方法定义
// 添加方案
const onAdd = () => {
  if (phaseList.value.length === 0) {
    ElMessage.error('请先配置相位！')
    return
  }

  if (patternList.value.length >= 32) {
    ElMessage.error('最多只能创建32个方案！')
    return
  }

  increaseId()
  const newPattern = createNewPattern()
  globalParamModel.addParamsByType('patternList', newPattern)

  // 重新排序
  const sortedPatterns = globalParamModel.getParamsByType('patternList')
  sortedPatterns.sort((a: PatternItem, b: PatternItem) => a.id - b.id)
}

// 创建新方案
const createNewPattern = (): PatternItem => {
  const pattern: PatternItem = {
    id: id.value,
    desc: `方案${id.value}`,
    offset: 0,
    cycle: 0,
    rings: [[], [], [], []],
    stagesList: [],
    contrloType: isRing.value ? 'stage' : 'ring'
  }

  if (isRing.value) {
    // 阶段模式：创建默认阶段
    pattern.stagesList = [{
      key: 0,
      stageNo: 1,
      green: 25,
      yellow: 3,
      red: 2,
      min: 15,
      max: 60,
      phases: [],
      stageSplit: 30,
      delaystart: 0,
      advanceend: 0
    }]
    pattern.cycle = 30
  } else {
    // 环模式：根据相位创建环配置
    phaseList.value.forEach((phase: PhaseItem) => {
      const ringItem: RingItem = {
        id: phase.id,
        name: `相位${phase.id}`,
        desc: getPhaseDescription(phase),
        value: 30,
        mode: 2,
        options: [],
        flowperhour: 0,
        saturation: 1700,
        delaystart: 0,
        advanceend: 0
      }

      const ringIndex = phase.ring - 1
      if (ringIndex >= 0 && ringIndex < 4) {
        pattern.rings[ringIndex].push(ringItem)
      }
    })

    // 计算周期
    pattern.cycle = calculateRingCycle(pattern.rings)
  }

  return pattern
}

// 计算环周期
const calculateRingCycle = (rings: RingItem[][]): number => {
  let maxCycle = 0
  for (const ring of rings) {
    if (ring.length === 0) continue
    let cycle = 0
    for (const item of ring) {
      if (item.mode !== 7) { // 忽略相位不计周期
        cycle += item.value
      }
    }
    if (cycle > maxCycle) {
      maxCycle = cycle
    }
  }
  return maxCycle
}

// 获取相位描述
const getPhaseDescription = (phase: PhaseItem): any[] => {
  const list: any[] = []

  if (phase.direction && phase.direction.length > 0) {
    phase.direction.forEach(dirId => {
      list.push({
        id: dirId,
        peddirection: phase.peddirection || []
      })
    })
  } else {
    list.push({
      id: '',
      peddirection: phase.peddirection || []
    })
  }

  return list
}

// 处理模式变化
const handleModeChange = () => {
  // 重新初始化活动标签页
  activeTabList.value = patternList.value.map(() => isRing.value ? 'stage' : 'ring')
  expandedRows.value = []
}

// 切换周期模式
const changeCycle = () => {
  cycleChange.value = !cycleChange.value
  buttonTextColor.value = cycleChange.value ? '#299BCC' : '#606266'
}

// 处理模式转换
const handleModeConvert = () => {
  convertDialogVisible.value = true
}

// 确认模式转换
const confirmModeConvert = () => {
  // 这里可以添加模式转换的具体逻辑
  ElMessage.success('模式转换完成！')
  convertDialogVisible.value = false
}

// 表格展开变化
const handleExpandChange = (row: PatternItem, expandedRowsData: PatternItem[]) => {
  // 这里不需要修改expandedRows.value，因为expandedRowsData是参数
  console.log('展开行变化:', row, expandedRowsData)
}

// 标签页点击
const handleTabClick = () => {
  // 标签页切换逻辑
}

// 添加阶段
const addStage = (stagesList: StageItem[]) => {
  if (stagesList.length >= 16) {
    ElMessage.error('最多只能创建16个阶段！')
    return
  }

  stagesList.push({
    key: stagesList.length,
    stageNo: stagesList.length + 1,
    green: 25,
    yellow: 3,
    red: 2,
    min: 15,
    max: 60,
    phases: [],
    stageSplit: 30,
    delaystart: 0,
    advanceend: 0
  })
}

// 处理描述变化
const handleDescChange = (pattern: PatternItem) => {
  // 描述变化处理逻辑
}

// 处理相位差变化
const handleOffsetChange = (pattern: PatternItem) => {
  if (pattern.offset < 0) {
    ElMessage.error('相位差不能小于0！')
    pattern.offset = 0
  }
  if (pattern.offset > pattern.cycle) {
    ElMessage.error('相位差不能大于周期！')
    pattern.offset = pattern.cycle
  }
}

// 处理环更新
const handleUpdateRing = (ringData: RingItem[], ringIndex: number, patternIndex: number) => {
  patternList.value[patternIndex].rings[ringIndex - 1] = ringData
  // 重新计算周期
  patternList.value[patternIndex].cycle = calculateRingCycle(patternList.value[patternIndex].rings)
}

// 处理阶段更新
const handleUpdateStage = (stageData: StageItem, stageIndex: number, patternIndex: number) => {
  patternList.value[patternIndex].stagesList[stageIndex] = stageData
  // 重新计算周期
  const stageCycle = patternList.value[patternIndex].stagesList.reduce((total, stage) => {
    return total + (stage.stageSplit || 0)
  }, 0)
  patternList.value[patternIndex].cycle = stageCycle
}

// 处理阶段删除
const handleDeleteStage = (stageIndex: number, patternIndex: number) => {
  if (patternList.value[patternIndex].stagesList.length <= 1) {
    ElMessage.error('至少需要保留一个阶段！')
    return
  }
  patternList.value[patternIndex].stagesList.splice(stageIndex, 1)
}

// 处理参数更新
const handleUpdateParams = (params: any, patternIndex: number) => {
  Object.assign(patternList.value[patternIndex], params)
}

// 处理其他配置更新
const handleUpdateOther = (otherConfig: any, patternIndex: number) => {
  Object.assign(patternList.value[patternIndex], otherConfig)
}

// 处理优化
const handleOptimize = (index: number, pattern: PatternItem) => {
  selectedPattern.value = { ...pattern }
  optimizeDialogVisible.value = true
}

// 执行优化
const executeOptimize = () => {
  if (isRing.value && stageOptimizeRef.value) {
    stageOptimizeRef.value.optimize()
  } else if (!isRing.value && patternOptimizeRef.value) {
    patternOptimizeRef.value.optimize()
  }
}

// 优化成功回调
const handleOptimizeSuccess = (optimizedData: any) => {
  if (selectedPattern.value) {
    const index = patternList.value.findIndex(p => p.id === selectedPattern.value!.id)
    if (index !== -1) {
      Object.assign(patternList.value[index], optimizedData)
    }
  }
  optimizeDialogVisible.value = false
  ElMessage.success('优化完成！')
}

// 处理克隆
const handleClone = (index: number, pattern: PatternItem) => {
  if (patternList.value.length >= 32) {
    ElMessage.error('最多只能创建32个方案！')
    return
  }

  increaseId()
  const clonedPattern = JSON.parse(JSON.stringify(pattern))
  clonedPattern.id = id.value
  clonedPattern.desc = `${pattern.desc}_副本`

  globalParamModel.addParamsByType('patternList', clonedPattern)

  // 重新排序
  const sortedPatterns = globalParamModel.getParamsByType('patternList')
  sortedPatterns.sort((a: PatternItem, b: PatternItem) => a.id - b.id)
}

// 处理删除
const handleDelete = (index: number, pattern: PatternItem) => {
  ElMessageBox.confirm(
    '确认删除此方案？',
    '提示',
    {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    globalParamModel.deleteParamsByType('patternList', index, 1)
    ElMessage.success('删除成功！')
  }).catch(() => {
    ElMessage.info('删除取消！')
  })
}

// 生命周期钩子
onMounted(() => {
  globalParamModel = store.getters.globalParamModel
  initializeData()
  setTableHeight()

  // 从本地存储恢复配置模式
  const savedMode = localStorage.getItem('patternMode')
  if (savedMode) {
    isRing.value = savedMode === 'stage'
  }
})
</script>

<style scoped lang="scss">
.app-container {
  padding: 20px;
}

.pattern-table {
  height: 100%;
}

.toolbar {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  gap: 20px;

  .tips {
    color: #909399;
    font-size: 12px;
    flex: 1;
  }

  .right-controls {
    display: flex;
    gap: 10px;
  }
}

.expand-content {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;
}

.ring-config,
.stage-config {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
}

.add-stage {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20px;
}

.pattern-diagram {
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.optimize-content {
  min-height: 400px;
}

:deep(.el-table) {
  .el-table__expand-icon {
    color: #409eff;
  }

  .cell {
    overflow: visible;
  }
}

:deep(.el-tabs) {
  .el-tabs__header {
    margin-bottom: 20px;
  }

  .el-tabs__content {
    padding: 0;
  }
}

:deep(.el-dialog) {
  .el-dialog__body {
    padding: 20px;
  }
}
</style>
      let rings = pattern.stagesList
      let stageCycleList = rings.map(item => {
        if (item.stageSplit) {
          return item.stageSplit
        } else {
          return 0
        }
      })
      let maxCycle = stageCycleList.reduce((a, b) => {
        return a + b
      }, 0)
      return maxCycle
    },
    getPattern () {
      let patternList = this.$store.state.globalParam.tscParam.patternList
      if (!patternList.length) return
      this.initData()
      for (let i = 0; i < patternList.length; i++) {
        if (patternList[i].contrloType === 'stage' || (this.isRing && !patternList[i].contrloType)) {
          this.isRing = true
          this.setStageList(patternList[i].stagesList, patternList[i].id)
          patternList[i].cycle = this.getMaxCycle(patternList[i])
        } else if (patternList[i].contrloType === 'ring' || (!this.isRing && !patternList[i].contrloType && patternList[i].rings.length > 0)) {
          this.isRing = false
          this.handleStageData(patternList[i].rings, patternList[i].id)
          // patternList[i].cycle = this.getringCycle(patternList[i])
        } else if (patternList[i].rings.length === 0 && patternList[i].stagesList.length > 0) {
          this.isRing = true
          this.setStageList(patternList[i].stagesList, patternList[i].id)
          patternList[i].cycle = this.getMaxCycle(patternList[i])
        }
      }
    },
    initData () {
      // 判断有几个环，就创建几个看板
      let phaseList = this.globalParamModel.getParamsByType('phaseList')
      this.coordphaseOption = phaseList
      this.overlap = this.globalParamModel.getParamsByType('overlaplList')
      this.agentId = getIframdevid()
      let rings = []
      if (phaseList.length === 0) {
        this.$store.getters.tscParam.patternList = []
        return
      }
      for (let phase of phaseList) {
        rings.push(phase.ring)
      }
      this.ringCount = Array.from(new Set(rings)) // 去除数组重复的元素
      this.ringCount = this.ringCount.sort(this.sortNumbers) // 把数组中的值按照从小到大的顺序重新排序
      this.ringCounts = Array.from(new Set(rings)) // 去除数组重复的元素
      this.ringCounts = this.ringCounts.sort(this.sortNumbers) // 把数组中的值按照从小到大的顺序重新排序
      this.optimizes = Array.from(new Set(rings)) // 去除数组重复的元素
      this.optimizes = this.optimizes.sort(this.sortNumbers) // 把数组中的值按照从小到大的顺序重新排序
      this.increaseId()
      this.getCycle()
      this.updatePhaseDescription()
      this.getOptionsOfRing()
    },
    sortNumbers (a, b) {
      return a - b
    },
    getPhase () {
      uploadSingleTscParam('phase').then(data => {
        let res = data.data
        if (!res.success) {
          if (res.code === '4003') {
            this.$message.error(this.$t('edge.errorTip.devicenotonline'))
            return
          }
          this.$message.error(getMessageByCode(data.data.code, this.$i18n.locale))
          return
        }
        this.phaseList = res.data.data.phaseList
      })
    },
    increaseId () { // 实现id在之前的基础上寻找最小的
      let patternList = this.globalParamModel.getParamsByType('patternList')
      let patternIdList = patternList.map(ele => ele.id)
      let i = patternList.length - 1
      if (i >= 0) {
        for (let j = 1; j <= 108; j++) {
          if (!patternIdList.includes(j)) {
            this.id = j
            return
          }
        }
      }
    },
    cancelOptimize () {
      this.dialogFormVisible = false
      this.selectedRow = null
    },
    optimize () {
      this.$refs.patternOptimize.optimize()
      this.dialogFormVisible = false
    },
    stageOptimizes () {
      this.$refs.stageOptimize.optimize()
      this.dialogFormVisible = false
    },
    handleOptimize (index, row) {
      this.selectedRow = { ...row }
      if (row.contrloType === 'stage') {
        const filterPhase = []
        this.selectedRow.stagesList.forEach(item => {
          if (item.phases && item.phases.length > 0) {
            filterPhase.push(...item.phases)
          }
        })
        const tableRings = filterPhase.map(item => {
          return {
            id: item,
            desc: this.getPhaseDescriptions(this.phaseList.filter(phase => phase.id === item)[0]),
            flowperhour: 0,
            saturation: 0
          }
        })
        this.tableRing = tableRings.reduce((accumulator, current) => {
            const x = accumulator.find(item => item.id === current.id)
            if (!x) {
                return accumulator.concat([current])
            } else {
                return accumulator
            }
        }, [])
      } else {
        const flatArray = this.selectedRow.rings.flat().filter(item => item !== null)
        this.tableRing = flatArray.map(item => {
          return {
            id: item.id,
            desc: this.getPhaseDescriptions(this.phaseList.filter(phase => phase.id === item.id)[0]),
            flowperhour: item.flowperhour ? item.flowperhour : 0,
            saturation: item.saturation ? item.saturation : 0
          }
        })
      }
      this.dialogFormVisible = true
    },
    optimizestage (id) {
      this.dialogFormVisible = false
      let patternList = this.globalParamModel.getParamsByType('patternList')
      let rings = patternList.filter(item => item.id === id)[0].stagesList
      let stageCycleList = rings.map(item => {
        return item.stageSplit ? item.stageSplit : item.split
      })
      let maxCycle = stageCycleList.reduce((a, b) => {
        return a + b
      }, 0)
      patternList.filter(item => item.id === id)[0].cycle = maxCycle
    },
    optimizesucess (id) {
      this.dialogFormVisible = false
      let patternList = this.globalParamModel.getParamsByType('patternList')
      let nowPattern = patternList.filter(item => item.id === id)[0].rings
      patternList.filter(item => item.id === id)[0].cycle = this.getMoveCycle(nowPattern)
    },
    handleDelete (index, value) {
      this.$confirm(this.$t('edge.pattern.deletetip'),
        this.$t('edge.common.alarm'), {
          confirmButtonText: this.$t('edge.common.confirm'),
          cancelButtonText: this.$t('edge.common.cancel'),
          type: 'warning'
        }).then(() => {
        if (value.id === this.patternStatusIndex) {
          // this.isShowPatternStatus = false
          this.patternStatusIndex = -1
        }
        this.globalParamModel.deleteParamsByType('patternList', index, 1)
        this.$message({
          type: 'success',
          message: this.$t('edge.common.deletesucess')
        })
      }).catch(() => {
        this.$message({
          type: 'info',
          message: this.$t('edge.common.deletecancel')
        })
      })
    },
    addPattern () {
      this.increaseId()
      let Pattern = {
        id: this.id,
        desc: `${this.$t('edge.pattern.pattern')}${this.id}`,
        offset: 0,
        cycle: 0,
        stagesList: [],
        rings: [[], [], [], []]
      }
      // var newPattern = Pattern
      const phaseList = this.globalParamModel.getParamsByType('phaseList')
      for (let phase of phaseList) {
        let ring = {}
        ring.name = this.$t('edge.phase.phase') + phase.id
        ring.desc = this.getPhaseDescription(phase)
        ring.id = phase.id
        ring.value = 30
        ring.mode = 2
        ring.options = []
        ring.flowperhour = 0
        ring.saturation = 1700
        ring.delaystart = 0
        ring.advanceend = 0
        if (phase.ring === 1) {
          Pattern.rings[0].push(ring)
        } else if (phase.ring === 2) {
          Pattern.rings[1].push(ring)
        } else if (phase.ring === 3) {
          Pattern.rings[2].push(ring)
        } else if (phase.ring === 4) {
          Pattern.rings[3].push(ring)
        }
        // pahseIndex++
      }
      Pattern.rings = this.sortBarrier(Pattern.rings, phaseList)
      for (let ring of Pattern.rings) {
        if (ring.length === 0) continue
        let cycle = 0
        for (let r of ring) {
          if (r.mode === 7) { // 忽略相位不计周期
            continue
          }
          cycle = cycle + r.value
        }
        Pattern.cycle = cycle
      }
      return Pattern
    },
    // 相位按屏障顺序排序
    sortBarrier (rings, phaseList) {
      let barriers = getBarrier(rings, phaseList)
      const rearrangedRings = rings.map((ringArray, index) => {
        if (barriers[index]) {
          const barrierData = barriers[index].data
          const idMap = new Map(ringArray.map(phase => [phase.id, phase]))
          return barrierData.map(id => idMap.get(id)).filter(phase => phase !== undefined)
        } else {
          return []
        }
      })
      return rearrangedRings
    },
    onAdd () {
      if (this.globalParamModel.getParamLength('phaseList') === 0) {
        this.$message.error(
          this.$t('edge.pattern.firstphase')
        )
        return
      }
      if (this.globalParamModel.getParamLength('patternList') >= 32) {
        this.$message.error(
          this.$t('edge.pattern.mostdata')
        )
        return
      }
      // this.globalParamModel.getParamsByType('patternList').push(this.addPattern())
      this.globalParamModel.addParamsByType('patternList', this.addPattern())
      this.getCycle()
      // 重新排序相位数组
      let patternList = this.globalParamModel.getParamsByType('patternList')
      patternList.sort(this.compareProperty('id'))
    },
    compareProperty (property) {
      return function (a, b) {
        var value1 = a[property]
        var value2 = b[property]
        return value1 - value2
      }
    },
    handleEdit (index, row) {
    },
    getCycle () {
      let patternList = this.globalParamModel.getParamsByType('patternList')
      for (let pattern of patternList) {
        if (!pattern.stages) return
          let stages = pattern.stages
          let modeId = stages.filter(item => item.length === 1)[0]
          for (let rings of pattern.rings) {
            let num = 0
          for (let i = 0; i < rings.length; i++) {
            if (rings[i].length !== 0) {
              if (rings[i].mode === 7 && (modeId && modeId[0] === rings[i].id)) {
                // 忽略相位不计周期
                continue
              }
              let value = Number(rings[i].value) || Number(rings[i].split) || 0
              num += value
              let id = rings[i].id
              rings[i].name = this.getDescription(id)
              rings[i].mode = rings[i].mode ? rings[i].mode : 2
            }
          }
          if (num !== 0) {
            pattern.cycle = num
            break
          }
        }
      }
    },
    getOptionsOfRing () {
      let patternList = this.globalParamModel.getParamsByType('patternList')
      for (let pattern of patternList) {
        if (pattern.rings.length === 0) return
        for (let rings of pattern.rings) {
          for (let ring of rings) {
            ring.name = this.getDescription(ring.id)
            ring.mode = ring.mode ? ring.mode : 2
            ring.options = this.getDecimalSystem(ring.options)
          }
        }
      }
    },
    getDecimalSystem (list) {
      if (!list) return
      let arr = []
      // if (list === null || list === undefined || list.length === 0) return arr
      if (list[0] === 1) arr.push(1)
      if (list[1] === 1) arr.push(2)
      if (list[2] === 1) arr.push(4)
      return arr
    },
    // 更新已有的pattern的箭头模型
    updatePhaseDescription () {
      let patternList = this.globalParamModel.getParamsByType('patternList')
      let phaseList = this.globalParamModel.getParamsByType('phaseList')
      for (let pattern of patternList) {
        if (pattern.rings.length === 0) return
        for (let phase of phaseList) {
          let ringIndex = phase.ring - 1 // 将 ring 转换为索引
          if (ringIndex >= 0 && ringIndex < pattern.rings.length) {
            let list = this.getPhaseDescription(phase)
            for (let obj of pattern.rings[ringIndex]) {
              if (obj.id === phase.id) {
                obj.desc = list
                // obj.mode = 2; // 如果需要，可以取消注释
              }
            }
          }
        }
      }
    },
    getDescription (id) {
      let phaseList = this.globalParamModel.getParamsByType('phaseList')
      for (let phase of phaseList) {
        if (phase.id === id) {
          if (phase.desc !== '' && phase.desc !== undefined) {
            return phase.desc
          } else {
            return this.$t('edge.phase.phase') + id
          }
        }
      }
    },
    getPedPhasePos () {
      // 行人相位信息
      this.sidewalkPhaseData = []
      let phaseList = this.globalParamModel.getParamsByType('phaseList')
      phaseList.forEach((ele, i) => {
        if (ele.peddirection) {
          ele.peddirection.forEach((dir, index) => {
            // 行人相位
            if (this.PhaseDataModel.getSidePos(dir)) {
              this.sidewalkPhaseData.push({
                key: this.CrossDiagramMgr.getUniqueKey('pedphase'),
                phaseid: ele.id, // 相位id，用于对应相位状态
                id: dir,
                name: this.PhaseDataModel.getSidePos(dir).name
              })
            }
          })
        }
      })
      return this.sidewalkPhaseData
    },
    getPhaseDescriptions (phaseList) {
      let list = []
      let peddirections = []
      let color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
      let sidewalkPhaseData = this.getPedPhasePos()
      for (let walk of sidewalkPhaseData) {
        if (phaseList.peddirection) {
          for (let ped of phaseList.peddirection) {
            // if (stg === walk.phaseid) {
            let obj = {}
            obj.name = walk.name
            obj.id = walk.id
            obj.color = color
            if (ped === walk.id) {
              peddirections.push(obj)
              peddirections = Array.from(new Set(peddirections))
            }
            // }
          }
        } else {
          peddirections = []
        }
      }
      if (phaseList.direction.length > 0) {
        for (let id of phaseList.direction) {
          let obj = {}
          obj.id = id
          obj.peddirection = peddirections
          obj.color = color
          list.push(obj)
        }
      } else {
        let obj = {}
        obj.id = ''
        obj.peddirection = peddirections
        obj.color = color
        list.push(obj)
      }
      return list
    },
    getPhaseDescription (phaseList) {
      let list = []
      let peddirections = []
      // let color = getTheme() === 'light' ? '#1E1E1E' : '#F1F3F4'
      let sidewalkPhaseData = this.getPedPhasePos()
      for (let walk of sidewalkPhaseData) {
        if (phaseList.peddirection) {
          for (let ped of phaseList.peddirection) {
            // if (stg === walk.phaseid) {
            let obj = {}
            obj.name = walk.name
            obj.id = walk.id
            // obj.color = color
            if (ped === walk.id) {
              peddirections.push(obj)
              peddirections = Array.from(new Set(peddirections))
            }
            // }
          }
        } else {
          peddirections = []
        }
      }
      if (phaseList.direction.length > 0) {
        for (let id of phaseList.direction) {
          let obj = {}
          obj.id = id
          obj.peddirection = peddirections
          // obj.color = color
          list.push(obj)
        }
      } else {
        let obj = {}
        obj.id = ''
        obj.peddirection = peddirections
        // obj.color = color
        list.push(obj)
      }
      return list
    },
    // getPhaseDescription (phaseList) {
    //   if (!phaseList) return
    //   let list = []
    //   for (let id of phaseList) {
    //     let obj = {}
    //     obj.id = id
    //     obj.color = '#454545'
    //     list.push(obj)
    //   }
    //   return list
    // },
    checkOffset (offset, val) {
      let cycle = val.cycle
      if (offset < 0) {
        this.$message.error('相位差不能小于0！')
      }
      if (offset > cycle) {
        this.$message.error('相位差不能大于周期！')
      }
    },
    handleSplit (index) {
      let currPattern = this.patternList[index]
      setTimeout(() => {
        this.handleStageData(currPattern.rings, this.patternList[index].id)
        // this.getPattern()
      }, 50)
    },
    handleSplitMove (id) {
      let patternList = this.globalParamModel.getParamsByType('patternList')
      let nowPattern = patternList.filter(item => item.id === id)[0].rings
      patternList.filter(item => item.id === id)[0].cycle = this.getMoveCycle(nowPattern)
    },
    getMoveCycle (pattern) {
      // let rings = pattern.rings
      let maxCycle = 0
      for (let ring of pattern) {
        if (ring.length === 0) continue
        let cycle = 0
        for (let r of ring) {
          if (r.mode === 7) { // 忽略相位不计周期
            continue
          }
          cycle = cycle + r.value
        }
        if (cycle > maxCycle) {
          maxCycle = cycle
        }
      }
      return maxCycle
    },
    handleMode (index) {
      this.initData()
    },
    handleClick (tab, event) {
      if (tab.paneName === 'stage') {
      }
    },
    setStageList (stageChange, id) {
      let patternList = this.globalParamModel.getParamsByType('patternList')
      let stagesList = []
      for (let i = 0; i < stageChange.length; i++) {
        let stage = JSON.parse(JSON.stringify(stageChange[i]))
        let stageItem = this.getStageItems(stage.phases, i, stageChange)
        stagesList.push(JSON.parse(JSON.stringify(stageItem)))
      }
      stagesList.forEach((stage) => {
        delete stage.split
      })
      patternList.map(item => { // 添加特征参数stage
        if (item.id === id) {
          if (this.isRing) {
            item.contrloType = 'stage'
          } else {
            item.contrloType = 'ring'
          }
          item.stagesList = JSON.parse(JSON.stringify(stagesList.filter(item => item.stageSplit)))
        }
      })
      this.stagesList = stagesList.filter(item => item.stageSplit)
    },
    handleStageData (rings, id) { // stagesList
      let phaseList = []
      let stagesList = []
      let phaseLists = this.globalParamModel.getParamsByType('phaseList')
      let patternList = this.globalParamModel.getParamsByType('patternList')
      this.phaseList = phaseLists
      // rings = JSON.parse(JSON.stringify(rings))
      if (rings.length === 0) return
      let mapAdd = rings.map(item => {
        return item.map(val => {
          return val.value + (val.sum ? val.sum : 0)
        })
      })
      let maxCycle = mapAdd.length > 0 ? mapAdd.map(item => {
        return item.length > 0 ? item.reduce((a, b) => {
          return a + b
        }) : 0
      }) : 0
      this.max = Math.max(...maxCycle)// 每个环的周期最大值
      this.stateList = [0]
      this.narr = []
      let currentIds = ''
      let lastCurrentIds = ''
      for (let j = 0; j <= this.max; j++) { // 指针长度
        for (let i = 0; i < rings.length; i++) { // 环列表
          let ring = rings[i]// 每个环对象
          let sum = 0
          for (let n = 0; n < ring.length; n++) { // 相位累计长度
            // if (ring[n].mode !== 7) {
            sum = sum + ring[n].value + (ring[n].sum ? ring[n].sum : 0)
            if (j < sum) {
              let phaseId = ring[n].id
              currentIds = currentIds + '-' + phaseId
              break
            }
            // }
          }
        }
        if (lastCurrentIds !== currentIds && lastCurrentIds !== '') { // 当前相位id和上一个相比不同说明相位变化了
          phaseList.push(lastCurrentIds)
          this.stateList.push(j)// 阶段累计长度的集合
        }
        lastCurrentIds = currentIds
        currentIds = ''
      }
      let newPhaselist = []
      phaseList.forEach(i => {
        let rangeArr = i.split('-').map(Number)
        if (rangeArr.length === 3) {
          newPhaselist.push([
            JSON.parse(JSON.stringify(rangeArr[1])),
            JSON.parse(JSON.stringify(rangeArr[2]))
          ])
        } else if (rangeArr.length === 4) {
          newPhaselist.push([
            JSON.parse(JSON.stringify(rangeArr[1])),
            JSON.parse(JSON.stringify(rangeArr[2])),
            JSON.parse(JSON.stringify(rangeArr[3]))
          ])
        } else if (rangeArr.length === 5) {
          newPhaselist.push([
            JSON.parse(JSON.stringify(rangeArr[1])),
            JSON.parse(JSON.stringify(rangeArr[2])),
            JSON.parse(JSON.stringify(rangeArr[3])),
            JSON.parse(JSON.stringify(rangeArr[4]))
          ])
        } else {
          newPhaselist.push([
            JSON.parse(JSON.stringify(rangeArr[1]))
          ])
        }
      })
      for (let i = this.stateList.length - 1; i >= 1; i--) {
        this.narr.push(this.stateList[i] - this.stateList[i - 1])
      }
      // newPhaselist
      this.narr.reverse()// 阶段差
      for (let i = 0; i < newPhaselist.length; i++) {
        let stage = JSON.parse(JSON.stringify(newPhaselist[i]))
        let newStage = []
        stage.map(item => {
          for (let ring of rings) {
            for (let ringItem of ring) {
              if (item === ringItem.id && (ringItem.mode !== 7 && ringItem.mode !== 8 && ringItem.mode !== 9)) {
                newStage.push(item)
              }
            }
          }
        })
        let stageItem = this.getStageItem(newStage, stage, rings, i)
        stagesList.push(JSON.parse(JSON.stringify(stageItem)))
      }
      patternList.map(item => { // 添加特征参数stage
        if (item.id === id) {
          item.contrloType = 'ring'
          if (this.overlap.length > 0) {
            item.overlapList = JSON.parse(JSON.stringify(stagesList))
            item.overlapCycle = stagesList.reduce((prev, curr) => prev + parseInt(curr.split), 0)
          }
          item.stages = JSON.parse(JSON.stringify(newPhaselist))
          item.cycle = this.getChangeCycle(item, JSON.parse(JSON.stringify(newPhaselist)))
          // stagesList.forEach((stage) => {
          //   delete stage.split
          // })
          // if (this.isChange) {
          item.stagesList = JSON.parse(JSON.stringify(stagesList))
          // } else {
          // item.stagesList = stagesList
          // }
        }
      })
      this.stagesList = JSON.parse(JSON.stringify(stagesList))
    },
    getChangeCycle (pattern, stages) {
      let modeId = stages.filter(item => item.length === 1)[0]
      let rings = pattern.rings
      let maxCycle = 0
      for (let ring of rings) {
        if (ring.length === 0) continue
        let cycle = 0
        for (let r of ring) {
          if (r.mode === 7 && (modeId && modeId[0] === r.id)) { // 忽略相位不计周期
            continue
          }
          cycle = cycle + r.value
        }
        if (cycle > maxCycle) {
          maxCycle = cycle
        }
      }
      return maxCycle
    },
    getStageItems (stageArr, i, stageChange) {
      const _that = this
      let res = {
        key: i,
        // split: this.narr[i], // 阶段绿性比
        phases: stageArr,
        delaystart: 0,
        advanceend: 0
      }
      // for (let rings of stageChange) {
      //   if (i === rings.key) {
      //     res.green = rings.green ? rings.green : rings.green === 0 ? 0 : 25
      //     res.yellow = rings.yellow ? rings.yellow : rings.yellow === 0 ? 0 : 3
      //     res.red = rings.red ? rings.red : rings.red === 0 ? 0 : 2
      //     res.phases = rings.phases ? rings.phases : stageArr
      //     // res.stageSplit = rings.split ? rings.split : 30
      //     if (this.isChange && rings.split) {
      //       res.stageSplit = rings.split
      //     } else if (rings.stageSplit) {
      //       res.stageSplit = rings.stageSplit
      //     } else {
      //       res.stageSplit = 0
      //     }
      //   }
      // }
      for (let j = 0; j < stageChange.length; j++) {
        if (i === j) {
          res.green = stageChange[j].green ? stageChange[j].green : stageChange[j].green === 0 ? 0 : 25
          res.yellow = stageChange[j].yellow ? stageChange[j].yellow : stageChange[j].yellow === 0 ? 0 : 3
          res.red = stageChange[j].red ? stageChange[j].red : stageChange[j].red === 0 ? 0 : 2
          res.stageNo = stageChange[j].stageNo ? stageChange[j].stageNo : 1
          if (_that.atctype === 'asc-g') {
            res.min = stageChange[j].min ? stageChange[j].min : stageChange[j].min === 0 ? 0 : 15
            res.max = stageChange[j].max ? stageChange[j].max : stageChange[j].max === 0 ? 0 : 60
          }
          if (_that.atctype === 'asc-g' && _that.stageparamList && _that.stageparamList.length > 0) {
            if (_that.stageparamList.filter(item => item.stageno === stageChange[j].stageNo).length > 0) {
              res.phases = _that.stageparamList.filter(item => item.stageno === stageChange[j].stageNo)[0].signalgroupstatuslist.signalgroupstatus.map(item => item.signalgroupno)
            }
          } else {
            res.phases = stageChange[j].phases ? stageChange[j].phases : stageArr
          }
          // res.stageSplit = stageChange[j].split ? stageChange[j].split : 30
          if (stageChange[j].split) {
            // 点环转阶段的时候
            // if (this.isChange && stageChange[j].split) {
            res.stageSplit = stageChange[j].split
          } else if (stageChange[j].stageSplit) {
            res.stageSplit = stageChange[j].stageSplit
          } else {
            res.stageSplit = 0
          }
        }
      }
      return res
    },
    getStageItem (stageArr, stage, ringsList, i) {
      let res = {
        key: i,
        split: this.narr[i], // 阶段绿性比
        phases: stageArr,
        // stageKanban: stage,
        delaystart: 0,
        advanceend: 0
      }
      // let splitArr = []
      let delaystartArr = []
      let advanceendArr = []
      for (let rings of ringsList) {
        for (let ring of rings) {
          if (stageArr.includes(ring.id)) {
            // let split = ring.value
            let delaystart = ring.delaystart
            let advanceend = ring.advanceend
            // splitArr.push(split)
            delaystartArr.push(delaystart)
            advanceendArr.push(advanceend)
          }
        }
      }
      // splitArr.sort(function (a, b) { return a - b })
      delaystartArr.sort(function (a, b) { return b - a })
      advanceendArr.sort(function (a, b) { return a - b })
      // res.split = splitArr.length > 0 ? splitArr[0] : 0
      res.delaystart = delaystartArr.length > 0 ? delaystartArr[0] : 0
      res.advanceend = advanceendArr.length > 0 ? advanceendArr[0] : 0
      return res
    },
    onStageSplitChange (diff, rowIndex, subIndex) {
      let stageArr = this.stagesList[subIndex].stages
      let row = this.patternList[rowIndex]
      let ringsList = row.rings
      for (let rings of ringsList) {
        for (let ring of rings) {
          if (stageArr.includes(ring.id)) {
            ring.value = (ring.value ? ring.value : 0) + diff
            continue
          }
        }
      }
    },
    stageSplitChange (diff, rowIndex, subIndex) {
      let row = this.patternList[rowIndex]
      let ringsList = row.stagesList
      for (let rings of ringsList) {
        // if (subIndex === rings.key) {
        rings.stageSplit = (rings.green ? rings.green : 0) + (rings.yellow ? rings.yellow : 0) + (rings.red ? rings.red : 0)
        continue
        // }
      }
    },
    onStageDelaystartChange (diff, rowIndex, subIndex) {
      let stageArr = this.stagesList[subIndex].stages
      let row = this.patternList[rowIndex]
      let ringsList = row.rings
      for (let rings of ringsList) {
        for (let ring of rings) {
          if (stageArr.includes(ring.id)) {
            ring.delaystart = (ring.delaystart ? ring.delaystart : 0) + diff
            continue
          }
        }
      }
    },
    onStageAdvanceendChange (diff, rowIndex, subIndex) {
      let stageArr = this.stagesList[subIndex].stages
      let row = this.patternList[rowIndex]
      let ringsList = row.rings
      for (let rings of ringsList) {
        for (let ring of rings) {
          if (stageArr.includes(ring.id)) {
            ring.advanceend = (ring.advanceend ? ring.advanceend : 0) + diff
            continue
          }
        }
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.dialog-form {
  // z-index: 99999 !important;
}
.stage-item {
  cursor: pointer;
  width: 100%;
  height: auto;
  margin: 5px 0;
  text-align: left;
  line-height: 40px;
  padding: 1px 1px;
  box-sizing: border-box;

  .el-select .el-input__inner {
    vertical-align: bottom !important;
  }
}

.stageAdds {
  margin-left: 30px;
}

.board-column-header {
  position: relative;
}

.optimize {
  margin-left: 10px !important;
}
.board {
  // width: 100%;
  display: flex;
  justify-content: center;
  flex-direction: row;
  align-items: flex-start;
}

/deep/.el-table .cell {
  overflow: unset;
  line-height: unset;
}

.deleteIcon {
  position: absolute;
  top: 0;
  right: -10px;
}

.kanban {
  &.todo {
    .board-column-header {
      background: #4a9ff9;
    }
  }
}

.stage-panel-contener {
  overflow-x: auto;
  display: flex;
  flex-direction: row;
  align-content: flex-start;
  align-items: center;
}

.special-params {
  width: 450PX;
  position: relative;
  float: left;
  // height: 40PX;
  // display: flex;
  // justify-content: flex-start;
  // align-items: center;
  padding-left: 10PX;
  font-size: 14PX;
  .tb-edit .current-row .el-input-number {
    margin-left: 18PX;
  }
}
</style>
