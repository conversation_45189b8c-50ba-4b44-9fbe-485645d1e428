<svg id="方位锁定" xmlns="http://www.w3.org/2000/svg" width="54" height="30.12" viewBox="0 0 54 30.12">
  <defs>
    <style>
      .cls-1, .cls-2 {
        fill: #7ccc66;
      }

      .cls-2, .cls-4 {
        fill-rule: evenodd;
      }

      .cls-3 {
        fill: #f27979;
      }

      .cls-4 {
        fill: #313131;
      }
    </style>
  </defs>
  <rect id="矩形_3708_拷贝_7" data-name="矩形 3708 拷贝 7" class="cls-1" x="20.6" y="16.81" width="33.4" height="9"/>
  <path id="形状_2744_拷贝_3" data-name="形状 2744 拷贝 3" class="cls-2" d="M2444.19,1010h-4.39a1.765,1.765,0,0,1-1.81-1.71v-2.14a1.765,1.765,0,0,1,1.81-1.71h4.39a1.765,1.765,0,0,1,1.81,1.71v2.14A1.765,1.765,0,0,1,2444.19,1010Zm-0.5-6.27v-0.93a1.7,1.7,0,0,0-3.39,0v0.93h-0.5a1.874,1.874,0,0,0-.76.15v-1.08a2.959,2.959,0,0,1,5.91,0v1.08a1.909,1.909,0,0,0-.76-0.15h-0.5Z" transform="translate(-2421.31 -1000)"/>
  <rect id="矩形_3708_拷贝_7-2" data-name="矩形 3708 拷贝 7" class="cls-3" y="16.81" width="20.78" height="9"/>
  <path id="形状_3710_拷贝_3" data-name="形状 3710 拷贝 3" class="cls-2" d="M2441.42,1012.4h1.8v17.71h-1.8V1012.4Z" transform="translate(-2421.31 -1000)"/>
  <path id="形状_1071_拷贝_5" data-name="形状 1071 拷贝 5" class="cls-4" d="M2447,1022.26h15.05V1024l3.96-2.47-3.96-2.54v1.82H2447v1.45Z" transform="translate(-2421.31 -1000)"/>
</svg>
