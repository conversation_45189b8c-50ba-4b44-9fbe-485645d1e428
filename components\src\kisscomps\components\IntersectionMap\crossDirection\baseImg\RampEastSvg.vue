/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <svg
    version="1.1"
    id="图层_1"
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    x="0px"
    y="0px"
    viewBox="0 0 870 650"
    style="enable-background: new 0 0 870 650"
    xml:space="preserve"
    :width="Width"
    :height="Height"
  >
    <rect x="0.7" y="340.6" class="st0" width="869.6" height="3" />
    <g class="st1">
      <g class="st2">
        <g>
          <rect x="-0.2" y="386" class="st3" width="14.4" height="2" />
        </g>
        <g>
          <path
            class="st3"
            d="M39.8,386h25.5v2H39.8V386z M90.8,386h25.5v2H90.8V386z M141.9,386h25.5v2h-25.5V386z M192.9,386h25.5v2
       h-25.5V386z M244,386h25.5v2H244V386z M295.1,386h25.5v2h-25.5V386z M346.1,386h25.5v2h-25.5V386z M397.2,386h25.5v2h-25.5V386z
        M448.3,386h25.5v2h-25.5V386z M499.3,386h25.5v2h-25.5V386z M550.4,386h25.5v2h-25.5V386z M601.4,386H627v2h-25.5L601.4,386
       L601.4,386z M652.5,386H678v2h-25.5V386z M703.6,386h25.5v2h-25.5V386z M754.6,386h25.5v2h-25.5V386z M805.7,386h25.5v2h-25.5
       V386z"
          />
        </g>
        <g>
          <rect x="856.8" y="386" class="st3" width="13" height="2" />
        </g>
      </g>
    </g>
    <g class="st1">
      <g class="st2">
        <g>
          <rect x="-0.2" y="447.2" class="st3" width="14.4" height="2" />
        </g>
        <g>
          <path
            class="st3"
            d="M39.8,447.2h25.5v2H39.8V447.2z M90.8,447.2h25.5v2H90.8V447.2z M141.9,447.2h25.5v2h-25.5V447.2z
        M192.9,447.2h25.5v2h-25.5V447.2z M244,447.2h25.5v2H244V447.2z M295.1,447.2h25.5v2h-25.5V447.2z M346.1,447.2h25.5v2h-25.5
       V447.2z M397.2,447.2h25.5v2h-25.5V447.2z M448.3,447.2h25.5v2h-25.5V447.2z M499.3,447.2h25.5v2h-25.5V447.2z M550.4,447.2h25.5
       v2h-25.5V447.2z M601.4,447.2H627v2h-25.5L601.4,447.2L601.4,447.2z M652.5,447.2H678v2h-25.5V447.2z M703.6,447.2h25.5v2h-25.5
       V447.2z M754.6,447.2h25.5v2h-25.5V447.2z M805.7,447.2h25.5v2h-25.5V447.2z"
          />
        </g>
        <g>
          <rect x="856.8" y="447.2" class="st3" width="13.1" height="2" />
        </g>
      </g>
    </g>
    <g>
      <g>
        <path
          class="st4"
          d="M869.9,124.7C844.5,134.5,721,182.3,713,184l-213.6,71.6c0,0-38.5,10.3-43.1,10.3c-4.5,0-46.2,8.9-46.2,8.9
     s-100.4,15.6-117.2,16c0,0-143.1,15.6-151.1,15l-141.9,9.4v9.6v1.2v5.7v7.3v182.6h870v-16.4V344.4l0,0L869.9,124.7L869.9,124.7z"
        />
      </g>
      <g>
        <line class="st5" x1="293" y1="288" x2="293.4" y2="289" />
      </g>
      <g>
        <line class="st5" x1="308.2" y1="305" x2="308.2" y2="305" />
      </g>
      <g>
        <rect
          x="592.8"
          y="235.2"
          transform="matrix(0.953 -0.3031 0.3031 0.953 -52.0097 192.699)"
          class="st6"
          width="4"
          height="57.5"
        />
      </g>
      <g>
        <rect x="-0.1" y="508.1" class="st6" width="870" height="2" />
      </g>
      <g>
        <polygon class="st7" points="0.2,542 869.7,542 869.8,540 0.2,540" />
      </g>
      <g>
        <path class="st8" d="M422.7,326" />
      </g>
      <g>
        <polygon
          class="st9"
          points="11.3,327 273.4,327 284.9,325 -0.1,325 -0.1,327"
        />
      </g>
      <g>
        <polygon class="st9" points="534.5,326.9 534.8,326 531.6,326" />
      </g>
      <g>
        <polygon class="st9" points="554.8,326.5 554.9,326.1 553.5,326.1" />
      </g>
      <g>
        <polygon class="st9" points="578.5,326.5 578.6,326.1 576.3,326.1" />
      </g>
      <g>
        <path
          class="st9"
          d="M167.3,318.9l-4.1-5.1c-2.7,0.1-5.4,0.2-8.1,0.3l-0.4,0.3l4.4,5.4l-2.7,5.5l4.8,1.6l2.1,0.1L167.3,318.9z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M145,320.4l-3.9-4.4c-2.7-0.1-5.4-0.2-8.1-0.2l-0.4,0.2l4.2,4.7l-3,4.6l4.9,1.4l2.1,0.2L145,320.4z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M122.7,321.2l-4-3.7c-2.7-0.1-5.4-0.2-8.1-0.3l-0.4,0.2l4.3,3.9l-2.9,3.7l4.9,1.2l2.1,0.2L122.7,321.2z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M190.6,318.1l-5.5-6.3c-2.5,0.2-5,0.4-7.5,0.7l-0.7,0.6l5.2,5.9l-4.7,8h8.1L190.6,318.1z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M211.8,318.9l-6-8.9c-1.9,0.2-3.8,0.3-5.7,0.5l-1.6,1.1l4.5,6.7l-7.9,8.7h9.4L211.8,318.9z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M236.1,316.4l-8-8.6c-2.2,0.2-4.5,0.4-6.7,0.6l-1.2,1.1l6.4,6.8l-8,8.8l1.8,1.6h6.1L236.1,316.4z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M258.8,314.6l-7-9.2c-2.7,0.3-5.4,0.6-8.2,0.8l6.5,8.5l-7.6,10.8l1.9,1.3h5.8L258.8,314.6z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M273.5,327l11.1-13.7l-9-10.4c-2.8,0.3-5.6,0.6-8.5,0.9l8.3,9.6l-10,12.3l1.4,1.2L273.5,327L273.5,327z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M311.4,306.5l-9.7-6.7c-3.5,0.4-7,0.8-10.5,1.2l10.2,7.1l-10.5,14.2l0.7,0.5l9.3-2.3L311.4,306.5z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M336.6,303l-9-6.2c-3.5,0.4-6.9,0.9-10.4,1.3l9.5,6.6l-9,12.2l10.7-2.6L336.6,303z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M363.3,297.9l-9-5c-3.5,0.3-6.9,0.7-10.4,1l9.5,5.3l-9,9.9l10.7-2.1L363.3,297.9z"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M391.8,291.6l-9.2-3.1c-3.5,0.5-6.8,1-10.3,1.5l9.7,3.3l-8.5,7.9l10.6-2.3L391.8,291.6z"
        />
      </g>
      <g>
        <polygon
          class="st10"
          points="869.9,520.5 869.9,520.5 869.9,520.5 2.3,520.5 0.9,520.5 -0.1,520.5 -0.1,522.5 869.9,522.5"
        />
      </g>
      <g>
        <path
          class="st9"
          d="M478.6,269.8C338.4,304,76.9,319.6-0.1,323.8v3.2h1v-1.3c73.3-3.8,306.6-17.9,450.7-47.8
     c-65,19.3-128.6,36.5-184.7,49.1h6.6h2.4c4.8-1.1,9.7-2.3,14.6-3.4l38.3-9.5c43-11,88.6-23.9,134.8-37.7l60.7-18.7l0,0
     c136.8-43,270.1-91.1,345.5-121.5V134C786.4,167.6,630.8,223.5,478.6,269.8z"
        />
      </g>
      <g>
        <path
          class="st10"
          d="M479.7,259.5C339.8,293.9,77.2,309.7-0.1,313.8v2l1-0.1l0,0c78.4-4.1,339.7-20,479.3-54.3h0.1
     C632,215.3,784.7,160.5,869.9,126.2V124C784.9,158.3,631.8,213.3,479.7,259.5z"
        />
      </g>
      <g>
        <path
          class="st7"
          d="M479.7,243.5C339.8,277.9,77.2,293.7-0.1,297.8v2l1-0.1l0,0c78.4-4.1,339.7-20,479.3-54.3h0.1
     C632,199.3,784.7,144.5,869.9,110.2V108C784.9,142.3,631.8,197.3,479.7,243.5z"
        />
      </g>
      <g id="直行_1_">
        <polygon
          class="st11"
          points="670.1,414.7 625.1,414.7 625.1,408 608.5,417.5 625.1,426.8 625.1,420.2 670.1,420.2"
        />
      </g>
      <g id="支路箭头">
        <polygon
          class="st11"
          points="707.9,223.3 665.5,238.2 663.2,231.8 650.7,246.3 669.5,249.6 667.3,243.4 709.7,228.5"
        />
      </g>
      <g id="XMLID_28_">
        <g>
          <rect x="0.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="0.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_27_">
        <g>
          <rect x="36.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="36.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_26_">
        <g>
          <rect x="72.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="72.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_25_">
        <g>
          <rect x="108.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="108.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_24_">
        <g>
          <rect x="144.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="144.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_23_">
        <g>
          <rect x="180.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="180.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_22_">
        <g>
          <rect x="216.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="216.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_21_">
        <g>
          <rect x="252.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="252.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_3_">
        <g>
          <rect x="288.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="288.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_10_">
        <g>
          <rect x="316.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="316.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_9_">
        <g>
          <rect x="352.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="352.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_8_">
        <g>
          <rect x="388.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="388.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_7_">
        <g>
          <rect x="424.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="424.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_6_">
        <g>
          <rect x="460.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="460.1" y="386" class="st6" width="12" height="2" />
      <g>
        <rect x="295.8" y="324.4" class="st6" width="12" height="3.3" />
        <rect x="331.8" y="324.4" class="st6" width="12" height="3.3" />
        <rect x="367.8" y="324.4" class="st6" width="12" height="3.3" />
        <rect x="403.8" y="324.4" class="st6" width="12" height="3.3" />
        <rect x="439.8" y="324.4" class="st6" width="12" height="3.3" />
        <rect x="475.8" y="324.4" class="st6" width="12" height="3.3" />
      </g>
      <g id="XMLID_5_">
        <g>
          <rect x="496.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="496.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_4_">
        <g>
          <rect x="532.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="532.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_2_">
        <g>
          <rect x="568.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="568.1" y="386" class="st6" width="12" height="2" />
      <g id="XMLID_1_">
        <g>
          <rect x="604.1" y="447.2" class="st6" width="12" height="2" />
        </g>
      </g>
      <rect x="604.1" y="386" class="st6" width="12" height="2" />
      <g>
        <g id="XMLID_19_">
          <g>
            <rect x="640.1" y="447.2" class="st6" width="12" height="2" />
          </g>
        </g>
        <rect x="640.1" y="386" class="st6" width="12" height="2" />
        <g id="XMLID_18_">
          <g>
            <rect x="676.1" y="447.2" class="st6" width="12" height="2" />
          </g>
        </g>
        <rect x="676.1" y="386" class="st6" width="12" height="2" />
        <g id="XMLID_17_">
          <g>
            <rect x="712.1" y="447.2" class="st6" width="12" height="2" />
          </g>
        </g>
        <rect x="712.1" y="386" class="st6" width="12" height="2" />
        <g id="XMLID_16_">
          <g>
            <rect x="748.1" y="447.2" class="st6" width="12" height="2" />
          </g>
        </g>
        <rect x="748.1" y="386" class="st6" width="12" height="2" />
        <g id="XMLID_15_">
          <g>
            <rect x="784.1" y="447.2" class="st6" width="12" height="2" />
          </g>
        </g>
        <rect x="784.1" y="386" class="st6" width="12" height="2" />
        <g id="XMLID_14_">
          <g>
            <rect x="820.1" y="447.2" class="st6" width="12" height="2" />
          </g>
        </g>
        <rect x="820.1" y="386" class="st6" width="12" height="2" />
        <g id="XMLID_13_">
          <g>
            <rect x="856.1" y="447.2" class="st6" width="12" height="2" />
          </g>
        </g>
        <rect x="856.1" y="386" class="st6" width="12" height="2" />
      </g>
      <polygon
        class="st9"
        points="500.8,325 500.8,327 785.8,327 868.8,327 869.9,327 869.9,325"
      />
      <g>
        <polygon
          class="st6"
          points="863.7,203.4 853.1,206.9 870.3,220.4 870.3,208.6"
        />
        <polygon
          class="st6"
          points="814.5,219.8 870.3,263.4 842.4,326.4 854.2,326.4 870.3,287.8 870.3,251.3 825.2,216.3"
        />
        <polygon
          class="st6"
          points="776,232.7 825.1,271.1 800.6,326.5 812.2,326.5 836.7,267.9 786.7,229.1"
        />
        <polygon
          class="st6"
          points="737.5,245.5 780,278.8 758.9,326.6 770.3,326.6 791.6,275.6 748.2,242"
        />
        <polygon
          class="st6"
          points="699,258.4 734.9,286.5 717.1,326.7 728.4,326.6 746.5,283.3 709.8,254.8"
        />
        <polygon
          class="st6"
          points="660.5,271.3 689.8,294.2 675.4,326.8 686.4,326.7 701.3,291 671.3,267.7"
        />
        <polygon
          class="st6"
          points="622,284.1 644.7,301.9 633.6,326.8 644.5,326.8 656.2,298.7 632.8,280.5"
        />
        <polygon
          class="st6"
          points="583.5,297 599.6,309.6 591.9,326.9 602.6,326.9 611.1,306.4 594.4,293.4"
        />
        <polygon
          class="st6"
          points="545,309.9 554.5,317.3 550.2,327 560.6,327 566,314.1 555.9,306.2"
        />
      </g>
      <polygon
        class="st6"
        points="500.8,324.5 500.8,326.6 870.3,203.2 870.3,201"
      />
    </g>
  </svg>
</template>
<script>
export default {
  name: 'rampEastroad',
  data () {
    return {}
  },
  props: {
    Width: {
      type: String,
      default: '870px'
    },
    Height: {
      type: String,
      default: '650px'
    }
  },
  methods: {},
  mounted () {}
}
</script>
<style scoped>
.st0 {
  display: none;
  fill: #ccb63a;
}
.st1 {
  display: none;
}
.st2 {
  display: inline;
}
.st3 {
  fill: #561313;
}
.st4 {
  fill: #3d3939;
}
.st5 {
  fill: none;
}
.st6 {
  fill: #dddddd;
}
.st7 {
  fill: #e5e5d8;
}
.st8 {
  fill: none;
  stroke: #dddddd;
  stroke-width: 2;
  stroke-miterlimit: 10;
}
.st9 {
  fill: #e0e0e0;
}
.st10 {
  fill: #ccb63a;
}
.st11 {
  fill: #fcfcfc;
}
</style>
