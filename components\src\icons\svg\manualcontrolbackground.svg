<svg id="手动控制背景" xmlns="http://www.w3.org/2000/svg" width="782.5" height="554" viewBox="0 0 782.5 554">
  <defs>
    <style>
      .cls-1, .cls-2 {
        fill: #a5a5a5;
        fill-opacity: 0.5;
        opacity: 0.4;
      }

      .cls-2, .cls-4, .cls-6 {
        fill-rule: evenodd;
      }

      .cls-3 {
        fill: #161616;
      }

      .cls-4 {
        fill: #9195a3;
        opacity: 0.2;
      }

      .cls-5, .cls-7 {
        font-size: 14px;
        text-anchor: end;
      }

      .cls-5, .cls-6 {
        fill: #fff;
      }

      .cls-6 {
        opacity: 0.7;
      }

      .cls-7 {
        fill: #9c9c9c;
      }
    </style>
  </defs>
  <path id="圆角矩形_988" data-name="圆角矩形 988" class="cls-1" d="M70.1,0H681.4a10,10,0,0,1,10,10V544a10,10,0,0,1-10,10H70.1a0,0,0,0,1,0,0V0A0,0,0,0,1,70.1,0Z"/>
  <path id="圆角矩形_988_拷贝" data-name="圆角矩形 988 拷贝" class="cls-2" d="M16058.9,2413h50.1a10.016,10.016,0,0,1,10,10v534a10.016,10.016,0,0,1-10,10h-50.1a9.931,9.931,0,0,1-9.9-10V2423A9.931,9.931,0,0,1,16058.9,2413Z" transform="translate(-15336.5 -2413)"/>
  <path id="圆角矩形_988_拷贝_2" data-name="圆角矩形 988 拷贝 2" class="cls-3" d="M10,0H70.1a0,0,0,0,1,0,0V554a0,0,0,0,1,0,0H10A10,10,0,0,1,0,544V10A10,10,0,0,1,10,0Z"/>
  <path id="形状_1009" data-name="形状 1009" class="cls-4" d="M15865.8,2698a45.287,45.287,0,0,0-42.3-45.15v-0.01h-0.1c-0.9-.06-1.9-0.15-2.9-0.15-0.5,0-1,.06-1.5.08a58.975,58.975,0,0,1-56-56c0-.49.1-0.97,0.1-1.46a28.447,28.447,0,0,0-.2-2.93v-0.09h0a45.247,45.247,0,0,0-90.3,0h0v0.08a28.835,28.835,0,0,0-.2,2.94c0,0.49.1,0.97,0.1,1.46a59.051,59.051,0,0,1-56,56c-0.5-.02-1-0.08-1.5-0.08-1,0-2,.09-2.9.15h-0.1v0.01a45.246,45.246,0,0,0,0,90.3v0.01h0.1c0.9,0.06,1.9.15,2.9,0.15,0.5,0,1-.06,1.5-0.08a59.051,59.051,0,0,1,56,56c0,0.49-.1.97-0.1,1.46a28.835,28.835,0,0,0,.2,2.94v0.08h0a45.247,45.247,0,0,0,90.3,0h0v-0.09a28.447,28.447,0,0,0,.2-2.93c0-.49-0.1-0.97-0.1-1.46a58.975,58.975,0,0,1,56-56c0.5,0.02,1,.08,1.5.08,1,0,2-.09,2.9-0.15h0.1v-0.01A45.287,45.287,0,0,0,15865.8,2698Z" transform="translate(-15336.5 -2413)"/>
  <g id="组_29" data-name="组 29">
    <text id="手动" class="cls-5" transform="translate(44.046 282)">手动</text>
    <path id="形状_2934" data-name="形状 2934" class="cls-6" d="M15387.5,2695a0.41,0.41,0,0,1-.3-0.18,0.514,0.514,0,0,1,0-.85l3.5-3.97-3.5-3.98a0.514,0.514,0,0,1,0-.85,0.441,0.441,0,0,1,.7,0l3.9,4.4a0.677,0.677,0,0,1,0,.85l-3.9,4.4A0.453,0.453,0,0,1,15387.5,2695Z" transform="translate(-15336.5 -2413)"/>
    <path id="形状_2934_拷贝" data-name="形状 2934 拷贝" class="cls-6" d="M15391.5,2695a0.41,0.41,0,0,1-.3-0.18,0.514,0.514,0,0,1,0-.85l3.5-3.97-3.5-3.98a0.514,0.514,0,0,1,0-.85,0.441,0.441,0,0,1,.7,0l3.9,4.4a0.677,0.677,0,0,1,0,.85l-3.9,4.4A0.453,0.453,0,0,1,15391.5,2695Z" transform="translate(-15336.5 -2413)"/>
  </g>
  <text id="自动" class="cls-7" transform="translate(760.477 284.001)">自动</text>
</svg>
