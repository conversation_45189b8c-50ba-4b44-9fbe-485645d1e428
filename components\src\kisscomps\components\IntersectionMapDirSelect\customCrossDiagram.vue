/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<!-- 仅支持基础道路相位方向选择（不包含匝道、公交相位） -->
<template>
  <div class="crossImg">
    <!-- 右行道路 B-->
    <div class="right-dir-road">
      <div class="centerText" v-if="crossType !== 'Customroads'">
      <!-- 手动刷新 -->
      <div v-if="!isLoaded">
        <RefreshSvg @click.native="refresh"/>
        <span class="text">{{$t('openatccomponents.overview.getintersectionmapagain')}}</span>
      </div>
    </div>
      <!-- 路口底图 -->
      <div class="baseImg">
        <!-- 城市道路 -->
        <CrossRoadsSvg v-if="crossType === 'Crossroads'"/>
        <TShapeEastRoadsSvg v-if="crossType === 'TypeT-east'"/>
        <TShapeWestRoadsSvg v-if="crossType === 'TypeT-west'"/>
        <TShapeNorthRoadsSvg v-if="crossType === 'TypeT-north'"/>
        <TShapeSouthRoadsSvg v-if="crossType === 'TypeT-south'"/>
        <!-- 其他路口 -->
        <CustomRoadsSvg v-if="crossType === 'Customroads'"/>
        <!-- 匝道 -->
        <!-- <RampEastRoadsSvg v-if="crossType === 'ramp-east'" />
        <RampWestRoadsSvg v-if="crossType === 'ramp-west'" />
        <RampNorthRoadsSvg v-if="crossType === 'ramp-north'" />
        <RampSouthRoadsSvg v-if="crossType === 'ramp-south'" /> -->
        <!-- 路段行人过街 -->
        <PedSectionSNSvg v-if="crossType === 'ped-section-south-north'" />
        <PedSectionEWSvg v-if="crossType === 'ped-section-east-west'" />
      </div>
      <!-- 城市道路状态-->
      <div v-if="mainType === '100' || mainType === '101' || mainType === '104'">
        <!-- 人行道 -->
        <div class="sidewalk" id="sidewalk" v-if="resetflag && isLoaded">
          <SidewalkClickSvg v-if="compSidewalkPhaseData.length" :Data="compSidewalkPhaseData" :clickMode="clickMode" @handleClickSidewalkIcon="handleClickSidewalkIcon" />
          <SidewalkSvg v-else v-for="(side, index) in compSidewalkPhaseData" :key="side.key + '-' + index" :Data="side" :crossType="crossType" />
        </div>
        <!-- 车道相位 -->
        <div v-if="resetflag" class="phaseIcon">
          <PhaseIconSvg v-for="(item, index) in compLanePhaseData" :key="item.key + '-' + index" :Data="item" :customClick="clickMode" :clickMode="clickMode" @handleClickPhaseIcon="handleClickPhaseIcon" />
        </div>
         <!-- 公交相位 -->
        <!-- <div v-if="resetflag" class="busIcon">
          <BusMapSvg v-for="(item, index) in comdireBusPhaseData" :key="'Busmap-' + item.key + '-' + index" :Data="item" />
          <PhaseIconSvg v-for="(item, index) in comdireBusPhaseData" :key="item.key + '-' + index" :Data="item"/>
        </div> -->
      </div>
      <!-- 匝道状态 -->
        <!-- 车道相位 -->
      <!-- <div v-if="resetflag && mainType === '103'">
        <RampPhaseIconSvg v-for="(item, index) in LanePhaseData" :key="item.key + '-' + index" :Data="item" />
      </div> -->
    </div>
    <!-- 右行道路 E-->
  </div>
</template>
<script>
import PhaseIconSvg from '../IntersectionMap/crossDirection/phaseIcon/phaseIconSvg'
import PhaseDataModel from '../IntersectionMap/crossDirection/utils'
import { getIntersectionInfo } from '../../../api/template.js'
import { uploadSingleTscParam } from '../../../api/param.js'
import CrossRoadsSvg from '../IntersectionMap/crossDirection/baseImg/CrossRoadsSvg'
import TShapeEastRoadsSvg from '../IntersectionMap/crossDirection/baseImg/TShapeEastRoadsSvg'
import TShapeWestRoadsSvg from '../IntersectionMap/crossDirection/baseImg/TShapeWestRoadsSvg.vue'
import TShapeNorthRoadsSvg from '../IntersectionMap/crossDirection/baseImg/TShapeNorthRoadsSvg.vue'
import TShapeSouthRoadsSvg from '../IntersectionMap/crossDirection/baseImg/TShapeSouthRoadsSvg.vue'
import CustomRoadsSvg from '../IntersectionMap/crossDirection/baseImg/CustomRoadsSvg.vue'
import RefreshSvg from '../IntersectionMap/crossDirection/baseImg/refreshSvg'
import SidewalkSvg from '../IntersectionMap/crossDirection/baseImg/SidewalkSvg'
import SidewalkClickSvg from '../IntersectionMap/crossDirection/baseImg/SidewalkClickSvg'
import RampEastRoadsSvg from '../IntersectionMap/crossDirection/baseImg/RampEastSvg'
import RampWestRoadsSvg from '../IntersectionMap/crossDirection/baseImg/RampWestSvg'
import RampNorthRoadsSvg from '../IntersectionMap/crossDirection/baseImg/RampNorthSvg'
import RampSouthRoadsSvg from '../IntersectionMap/crossDirection/baseImg/RampSouthSvg'
import RampPhaseIconSvg from '../IntersectionMap/crossDirection/phaseIcon/rampPhaseIconSvg'
import PedSectionEWSvg from '../IntersectionMap/crossDirection/baseImg/PedSectionEWSvg'
import PedSectionSNSvg from '../IntersectionMap/crossDirection/baseImg/PedSectionSNSvg'
// import { mapState } from 'vuex'
import LCrossRoadsSvg from '../IntersectionMap/crossDirection/baseImg/leftroad/LCrossRoadsSvg'
import LTShapeEastRoadsSvg from '../IntersectionMap/crossDirection/baseImg/leftroad/LTShapeEastRoadsSvg'
import LTShapeWestRoadsSvg from '../IntersectionMap/crossDirection/baseImg/leftroad/LTShapeWestRoadsSvg.vue'
import LTShapeNorthRoadsSvg from '../IntersectionMap/crossDirection/baseImg/leftroad/LTShapeNorthRoadsSvg.vue'
import LTShapeSouthRoadsSvg from '../IntersectionMap/crossDirection/baseImg/leftroad/LTShapeSouthRoadsSvg.vue'
import LPhaseIconSvg from '../IntersectionMap/crossDirection/phaseIcon/LphaseIconSvg'
import CrossDiagramMgr from '../../../EdgeMgr/controller/crossDiagramMgr.js'
import BusMapSvg from '../IntersectionMap/crossDirection/busIcon/busMapSvg'
import { getMessageByCode } from '../../../utils/responseMessage.js'

export default {
  name: 'crossDiagram',
  components: {
    PhaseIconSvg,
    CrossRoadsSvg,
    TShapeEastRoadsSvg,
    TShapeWestRoadsSvg,
    TShapeNorthRoadsSvg,
    TShapeSouthRoadsSvg,
    CustomRoadsSvg,
    RefreshSvg,
    SidewalkSvg,
    RampEastRoadsSvg,
    RampWestRoadsSvg,
    RampNorthRoadsSvg,
    RampSouthRoadsSvg,
    RampPhaseIconSvg,
    PedSectionEWSvg,
    PedSectionSNSvg,
    LCrossRoadsSvg,
    LTShapeEastRoadsSvg,
    LTShapeWestRoadsSvg,
    LTShapeNorthRoadsSvg,
    LTShapeSouthRoadsSvg,
    LPhaseIconSvg,
    BusMapSvg,
    SidewalkClickSvg
  },
  props: {
    agentId: {
      type: String
    },
    isShowMessage: {
      type: Boolean,
      default: true
    },
    choosedDirection: {
      type: Array,
      default: () => []
    },
    choosedPedDirection: {
      type: Array,
      default: () => []
    },
    clickMode: {
      type: Boolean,
      default: false
    },
    channelType: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    tempType: {
      handler: function (val) {
        this.getCrossType()
      }
    },
    agentId: {
      handler: function (val1, val2) {
        if (val1 !== val2) {
          this.init()
        }
      },
      immediate: true
    }
  },
  data () {
    return {
      LanePhaseData: [], // 车道相位数据
      overlapLanePhaseData: [], // 车道跟随相位数据
      ColorMap: new Map([[0, '#828282'], [1, '#ff2828'], [2, '#f7b500'], [3, '#77fb65'], [4, '#77fb65'], [5, '#f7b500']]), // 当前相位状态 --- 0：关灯, 1：红, 2：黄,  3：绿, 4：绿闪, 5：黄闪
      SidewalkColorMap: new Map([[0, '#828282'], [1, '#e24b4b'], [3, '#7bd66b']]),
      tempType: '', // 模版类型
      mainType: '101', // 路口形状
      mainDirection: '000', // 路口方向
      crossType: '', // 路口底图类型
      isLoaded: false, // 是否成功加载底图
      sidewalkPhaseData: [], // 行人相位
      overlapsidewalkPhaseData: [], // 行人跟随相位
      resetflag: true, // 离线后，控制行人相位、车道相位reset标识
      compLanePhaseData: [], // 对比车道相位和车道跟随相位后，显示的数据
      compSidewalkPhaseData: [], // // 对比行人相位和车道跟随相位后，显示的数据
      comdirePhaseData: [], // 对比相同方向车道相位数据后，被删减的唯一direction的数组
      comdireOverlapPhaseData: [], // 对比相同方向车道跟随相位数据后，被删减的唯一direction的数组
      busPhaseData: [], // 公交相位数据
      comdireBusPhaseData: [], //  对比相同方向公交车道数据后，被删减的唯一direction的数组
      channelStatusMap: new Map(), // 通道实时状态映射
      channelStatusList: [], // 通道实时状态列表
      phaseDirMap: new Map(),
      contrloType: 'ring'
    }
  },
  methods: {
    init () {
      this.CrossDiagramMgr = new CrossDiagramMgr()
      this.PhaseDataModel = new PhaseDataModel()
      this.getIntersectionInfo() // 获取路口信息
    },
    drawDefaultPhaseIcon () {
      this.compLanePhaseData = JSON.parse(JSON.stringify(this.CrossDiagramMgr.compare(this.LanePhaseData, this.overlapLanePhaseData, 'type', 'nostatus')))
      this.compSidewalkPhaseData = JSON.parse(JSON.stringify(this.CrossDiagramMgr.compare(this.sidewalkPhaseData, this.overlapsidewalkPhaseData, 'pedtype', 'nostatus')))
      this.comdireBusPhaseData = JSON.parse(JSON.stringify(this.busPhaseData))
      console.log(this.LanePhaseData)
      console.log(this.overlapLanePhaseData)
      console.log('###################', this.compLanePhaseData)
    },
    getIntersectionInfo () {
      // 获取路口信息
      this.contrloType = 'ring'
      const agentid = this.agentId
      getIntersectionInfo(agentid).then(res => {
        if (!res.data.success) {
          this.isLoaded = false
          let commomMsg = this.$t('openatccomponents.overview.signalID') + ' : ' + agentid
          let msg = getMessageByCode(res.data.code, this.$i18n.locale)
          if (res.data.data) {
            // 子类型错误
            let childErrorCode = res.data.data.errorCode
            if (childErrorCode) {
              let childerror = getMessageByCode(res.data.data.errorCode, this.$i18n.locale)
              msg = msg + ' - ' + childerror
            }
          }
          msg = msg + ' - ' + commomMsg
          // this.isShowMessage && this.$message.error(msg)
          if (this.isShowMessage) {
            console.log(msg)
          }
          return
        }
        this.isLoaded = true
        this.tempType = res.data.data.type
        // 获取车道相位、行人相位信息（坐标、名称）
        this.mainType = this.tempType.split('-')[0]
        this.mainDirection = this.tempType.split('-')[1]
        if (this.channelType) {
          this.getChannelInfo()
          return
        }
        this.getTempCrossInfo(res)
      })
    },
    getTempCrossInfo (res) {
      this.crossInfo = res.data.data.param
      this.crossInfo.phaseList.forEach(cross => this.phaseDirMap.set(cross.id, {direction: cross.direction, peddirection: cross.peddirection}))
      if (this.mainType === '100' || this.mainType === '101' || this.mainType === '104') {
        // 城市道路加载车道相位坐标和人行道坐标
        this.getPhasePos()
        this.getOverlapPhasePos()
        this.getPedPhasePos()
        this.getOverlapPedPhasePos()
      }
      this.inneChoosedDirection = [...this.choosedDirection]
      this.inneChoosedPedDirection = [...this.choosedPedDirection]
      this.drawDefaultPhaseIcon()
    },
    getBusPos () {
      // 公交相位信息
      this.busPhaseData = []
      this.crossInfo.phaseList.forEach((ele, i) => {
        if (ele.controltype >= 3 && ele.controltype <= 6) {
          ele.direction.forEach((dir, index) => {
          // 车道相位
            this.busPhaseData.push({
              key: this.CrossDiagramMgr.getUniqueKey('busphase'),
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
              name: this.PhaseDataModel.getBusPhasePos(dir).name,
              left: this.PhaseDataModel.getBusPhasePos(dir).x,
              top: this.PhaseDataModel.getBusPhasePos(dir).y,
              busleft: this.PhaseDataModel.getBusMapPos(dir).x,
              bustop: this.PhaseDataModel.getBusMapPos(dir).y,
              controltype: ele.controltype
            })
          })
        }
      })
      // 去掉重复方向的数据
      this.busPhaseData = Array.from(new Set(this.busPhaseData.map(item => item.id)))
        .map(id => this.busPhaseData.find(item => item.id === id))
    },
    getPhasePos () {
      // 车道相位信息
      this.LanePhaseData = []
      this.crossInfo.phaseList.forEach((ele, i) => {
        if (ele.controltype === undefined || ele.controltype <= 2) {
          ele.direction.forEach((dir, index) => {
          // 车道相位
            this.LanePhaseData.push({
              key: this.CrossDiagramMgr.getUniqueKey('phase'),
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
              name: this.PhaseDataModel.getPhase(dir).name,
              left: this.PhaseDataModel.getPhase(dir).x,
              top: this.PhaseDataModel.getPhase(dir).y
            })
          })
        }
      })
      // 去掉重复方向的数据
      this.LanePhaseData = Array.from(new Set(this.LanePhaseData.map(item => item.id)))
        .map(id => this.LanePhaseData.find(item => item.id === id))
    },
    getOverlapPhasePos () {
      // 车道跟随相位信息
      if (!this.crossInfo.overlaplList) return
      this.overlapLanePhaseData = []
      this.crossInfo.overlaplList.forEach((ele, i) => {
        if (ele.direction) {
          ele.direction.forEach((dir, index) => {
            this.overlapLanePhaseData.push({
              key: this.CrossDiagramMgr.getUniqueKey('overlapphase'),
              phaseid: ele.id, // 相位id，用于对应相位状态
              id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
              name: this.PhaseDataModel.getPhase(dir).name,
              left: this.PhaseDataModel.getPhase(dir).x,
              top: this.PhaseDataModel.getPhase(dir).y
            })
          })
        }
      })
      // 去掉重复方向的数据
      this.overlapLanePhaseData = Array.from(new Set(this.overlapLanePhaseData.map(item => item.id)))
        .map(id => this.overlapLanePhaseData.find(item => item.id === id))
    },
    getRampPhasePos () {
      // 匝道车道相位信息
      this.LanePhaseData = []
      this.crossInfo.phaseList.forEach((ele, i) => {
        ele.direction.forEach((dir, index) => {
          if (ele.controltype === 0) {
            this.handleRampPhasePosData(`${i}-${index}`, ele, dir, this.PhaseDataModel.getMainPhasePos)
          }
          if (ele.controltype === 1) {
            this.handleRampPhasePosData(`${i}-${index}`, ele, dir, this.PhaseDataModel.getSidePhasePos)
          }
        })
      })
      // 去掉重复方向的数据
      this.LanePhaseData = Array.from(new Set(this.LanePhaseData.map(item => item.id)))
        .map(id => this.LanePhaseData.find(item => item.id === id))
    },
    handleRampPhasePosData (key, phase, dir) {
      let posInfo = phase.controltype === 0 ? this.PhaseDataModel.getMainPhasePos(dir) : this.PhaseDataModel.getSidePhasePos(dir)
      this.LanePhaseData.push({
        key,
        controlType: phase.controltype,
        phaseid: phase.id, // 相位id，用于对应相位状态
        id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
        name: posInfo.name,
        left: posInfo.x,
        top: posInfo.y
      })
    },
    getPedPhasePos () {
      // 行人相位信息
      this.sidewalkPhaseData = []
      this.crossInfo.phaseList.forEach((ele, i) => {
        if (ele.peddirection) {
          ele.peddirection.forEach((dir, index) => {
          // 行人相位
            if (this.PhaseDataModel.getSidePos(dir)) {
              let key = this.CrossDiagramMgr.getUniqueKey('pedphase')
              this.sidewalkPhaseData.push({
                key,
                phaseid: ele.id, // 相位id，用于对应相位状态
                id: dir,
                name: this.PhaseDataModel.getSidePos(dir).name,
                left: this.PhaseDataModel.getSidePos(dir).x,
                top: this.PhaseDataModel.getSidePos(dir).y
              })
            }
          })
        }
      })
      // 去掉重复方向的数据
      this.sidewalkPhaseData = Array.from(new Set(this.sidewalkPhaseData.map(item => item.id)))
        .map(id => this.sidewalkPhaseData.find(item => item.id === id))
    },
    getOverlapPedPhasePos () {
      // 行人跟随相位信息
      if (!this.crossInfo.overlaplList) return
      this.overlapsidewalkPhaseData = []
      this.crossInfo.overlaplList.forEach((ele, i) => {
        if (ele.peddirection) {
          ele.peddirection.forEach((dir, index) => {
            if (this.PhaseDataModel.getSidePos(dir)) {
              this.overlapsidewalkPhaseData.push({
                key: this.CrossDiagramMgr.getUniqueKey('overlappedphase'),
                phaseid: ele.id, // 相位id，用于对应相位状态
                id: dir,
                name: this.PhaseDataModel.getSidePos(dir).name,
                left: this.PhaseDataModel.getSidePos(dir).x,
                top: this.PhaseDataModel.getSidePos(dir).y
              })
            }
          })
        }
      })
      // 去掉重复方向的数据
      this.overlapsidewalkPhaseData = Array.from(new Set(this.overlapsidewalkPhaseData.map(item => item.id)))
        .map(id => this.overlapsidewalkPhaseData.find(item => item.id === id))
    },
    getCrossType () {
      // 路口类型对应底图决策
      if (this.mainType === '101') {
        // 十字路口
        this.crossType = 'Crossroads'
      }
      if (this.mainType === '100') {
        // T型路口
        switch (this.mainDirection) {
          case '001': this.crossType = 'TypeT-east'
            break
          case '002': this.crossType = 'TypeT-south'
            break
          case '003': this.crossType = 'TypeT-west'
            break
          case '004': this.crossType = 'TypeT-north'
            break
        }
      }
      if (this.mainType === '103') {
        // 匝道
        switch (this.mainDirection) {
          case '001': this.crossType = 'ramp-east'
            break
          case '002': this.crossType = 'ramp-south'
            break
          case '003': this.crossType = 'ramp-west'
            break
          case '004': this.crossType = 'ramp-north'
            break
        }
      }
      if (this.mainType === '104') {
        // 路段行人过街
        switch (this.mainDirection) {
          case '005': this.crossType = 'ped-section-east-west'
            break
          case '006': this.crossType = 'ped-section-south-north'
            break
        }
      }
      if (this.mainType === '999') {
        // 其他路口
        this.crossType = 'Customroads'
      }
    },
    refresh () {
      this.getIntersectionInfo()
    },
    getChannelInfo () {
      uploadSingleTscParam('channel', this.agentId).then(data => {
        let res = data.data
        if (!res.success) {
          if (res.code === '4003') {
            // this.isShowMessage && this.$message.error(this.$t('openatccomponents.errorTip.devicenotonline'))
            if (this.isShowMessage) {
              console.log(this.$t('openatccomponents.errorTip.devicenotonline'))
            }
            return
          }
          // this.isShowMessage && this.$message.error(getMessageByCode(data.data.code, this.$i18n.locale))
          if (this.isShowMessage) {
            console.log(getMessageByCode(data.data.code, this.$i18n.locale))
          }
          return
        }
        let channelList = res.data.data.channelList.filter(ele => ele.type !== undefined)
        this.channelList = this.handleRepeatRealdir(channelList)
        console.log('this.channelList', this.channelList)
        this.handleChannelDirection()
      })
    },
    handleRepeatRealdir (channelList) {
      // 按realdir去掉重复方向的数据
      let dirChannelList = channelList.filter(ele => ele.realdir !== undefined && (ele.type === 0 || ele.type === 1 || ele.type === 3))
      let pedDirChannelList = channelList.filter(ele => ele.realdir !== undefined && ele.type === 2)
      let map = new Map()
      let map2 = new Map()
      dirChannelList.forEach(ele => {
        ele.realdir.forEach(dir => {
          if (map.get(dir) === undefined) {
            map.set(dir, ele)
          }
        })
      })
      pedDirChannelList.forEach(ele => {
        ele.realdir.forEach(dir => {
          if (map2.get(dir) === undefined) {
            map2.set(dir, ele)
          }
        })
      })
      let arr = Array.from(map)
      let pedarr = Array.from(map2)
      let newarr = []
      arr.forEach(ele => {
        ele[1].realdir = [ele[0]]
        let obj = {
          ...ele[1],
          realdir: [ele[0]]
        }
        newarr.push(obj)
      })
      pedarr.forEach(ele => {
        ele[1].realdir = [ele[0]]
        let obj = {
          ...ele[1],
          realdir: [ele[0]]
        }
        newarr.push(obj)
      })
      // console.log(newarr)
      return newarr
    },
    handleChannelDirection () {
      this.LanePhaseData = []
      this.sidewalkPhaseData = []
      this.sidewalkDir = []
      let realphasedirarr = []
      let realpeddirarr = []
      this.channelList.forEach((ele, i) => {
        if (ele.type === 0 || ele.type === 1 || ele.type === 3) {
          if (ele.realdir) {
            ele.realdir.forEach((dir, index) => {
            // 车道相位（通道类型是机动车，非机动车，公交时，对应相位机动车）
              this.LanePhaseData.push({
                key: this.CrossDiagramMgr.getUniqueKey('phase'),
                channelid: ele.id, // 通道id
                id: dir, // 接口返回的dir字段，对应前端定义的相位方向id，唯一标识
                name: this.PhaseDataModel.getPhase(dir).name,
                left: this.PhaseDataModel.getPhase(dir).x,
                top: this.PhaseDataModel.getPhase(dir).y
              })
            })
            realphasedirarr = Array.from(new Set(realphasedirarr.concat(ele.realdir)))
          }
        }
        if (ele.type === 2) {
          if (ele.realdir) {
            ele.realdir.forEach((dir, index) => {
              // 行人相位
              if (this.sidewalkDir.indexOf(dir) === -1 && this.PhaseDataModel.getSidePos(dir)) {
                let obj = {
                  key: this.CrossDiagramMgr.getUniqueKey('pedphase') + `-${this.agentId}`,
                  channelid: ele.id, // 通道id
                  id: dir,
                  name: this.PhaseDataModel.getSidePos(dir).name
                }
                if (this.channelType) {
                  obj.left = this.PhaseDataModel.getSidePos(dir).x
                  obj.top = this.PhaseDataModel.getSidePos(dir).y
                }
                this.sidewalkPhaseData.push(obj)
              }
            })
            realpeddirarr = Array.from(new Set(realpeddirarr.concat(ele.realdir)))
            this.sidewalkDir = Array.from(new Set([...this.sidewalkDir.concat(ele.realdir)]))
          }
        }
      })
      this.inneChoosedDirection = this.choosedDirection.filter(dir => realphasedirarr.indexOf(dir) !== -1)
      this.inneChoosedPedDirection = this.choosedPedDirection.filter(dir => realpeddirarr.indexOf(dir) !== -1)
      this.drawPhaseIcon()
    },
    async drawPhaseIcon () {
      const targetIds = [4, 8, 12, 16] // 掉头相位后画
      this.LanePhaseData = this.LanePhaseData.filter(item => !targetIds.includes(item.id)).concat(this.LanePhaseData.filter(item => targetIds.includes(item.id)))
      this.handleClickedPhase()
      this.compLanePhaseData = JSON.parse(JSON.stringify(this.LanePhaseData))
      this.handleClickedPedPhase()
      this.compSidewalkPhaseData = JSON.parse(JSON.stringify(this.sidewalkPhaseData))
    },
    handleClickedPhase () {
      for (let index = 0; index < this.LanePhaseData.length; index++) {
        const element = this.LanePhaseData[index]
        if (!this.inneChoosedDirection) return
        if (this.inneChoosedDirection.indexOf(element.id) !== -1) {
          element.clicked = true
        }
      }
    },

    handleClickPhaseIcon (key, action) {
      let curClickedPhase = {}
      if (action === 'clicked') {
        for (let index = 0; index < this.LanePhaseData.length; index++) {
          const element = this.LanePhaseData[index]
          if (element.key === key) {
            element.clicked = true
            curClickedPhase = JSON.parse(JSON.stringify(element))
          }
        }
      }
      if (action === 'cancle') {
        for (let index = 0; index < this.LanePhaseData.length; index++) {
          const element = this.LanePhaseData[index]
          if (element.key === key) {
            delete element.clicked
          }
        }
      }
      this.compLanePhaseData = JSON.parse(JSON.stringify(this.LanePhaseData))
      let clickedDirection = this.compLanePhaseData.filter(ele => ele.clicked && !ele.disabled)
      this.inneChoosedDirection = clickedDirection.map(ele => ele.id)
      this.EmitAllChoosedDirection(curClickedPhase)
    },
    handleClickedPedPhase () {
      // 排他
      for (let index = 0; index < this.sidewalkPhaseData.length; index++) {
        const element = this.sidewalkPhaseData[index]
        delete element.clicked
      }
      for (let index = 0; index < this.sidewalkPhaseData.length; index++) {
        const element = this.sidewalkPhaseData[index]
        if (!this.inneChoosedPedDirection) return
        if (this.inneChoosedPedDirection.indexOf(element.id) !== -1) {
          element.clicked = true
        }
      }
    },
    handleClickSidewalkIcon (data, curChoosePed) {
      this.clickedPedDirection = data.filter(ele => ele.clicked && !ele.disabled)
      this.inneChoosedPedDirection = this.clickedPedDirection.map(ele => ele.id)
      this.EmitAllChoosedDirection(curChoosePed)
    },
    EmitAllChoosedDirection (curClickedPhase) {
      let allChoosedDir = {
        direction: this.inneChoosedDirection,
        peddirection: this.inneChoosedPedDirection
      }
      this.$emit('handleClickCrossIcon', allChoosedDir, curClickedPhase)
      this.drawPhaseIcon()
    }
  },
  mounted () {
    // this.init()
  }
}
</script>
<style scoped>
.invisible {
  visibility: hidden;
}
.crossImg{
    position: relative;
    width: 870px;
    height: 650px;
    left: 50%;
    transform: translateX(-50%);
}
.centerText {
  position: absolute;
  width: 140px;
  height: 140px;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  /* text-align: center; */
  z-index: 9;
  display: flex;
  align-items: center;
  /* padding-left: 16px; */
}
.phaseCountdown {
  line-height: 42PX;
  font-size: 18px;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #fff;
  width: 150px;
  margin: 0 auto;
}
.countdownBg {
  border-radius: 10PX;
  background-color: rgba(94, 90, 90, 0.8);
  padding-left: 14PX;
  padding-top: 10PX;
  padding-bottom: 10PX;
}
.centerText .text {
  display: inline-block;
  color: #299BCC;
  margin-top: 20PX;
}
.merge-direction-icon {
  width: 80PX;
  height: 80PX;
  margin: 0 auto;
}

.baseImg {
    width: 100%;
    height: 650px;
    position: relative;
}
</style>
