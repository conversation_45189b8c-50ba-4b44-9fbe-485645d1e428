/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="detector-chart-icon">
    <drr
      :style="{'z-index': chooseIndex === DetectorChartData.index ? 9 : 0}"
      :x="item.x"
      :y="item.y"
      :w="item.w"
      :h="item.h"
      :selected="chooseIndex === DetectorChartData.index"
      :selectable="isSeletable"
      :angle="item.angle"
      :resizable="false"
      :rotatable="false"
      @select="handleSelectIcon(item)"
      @dragstop="boxDragStop(item, ...arguments)"
    >
      <div v-if="UsageMode === 'draw'" class="centerText" :class="{'defaultBg': chooseIndex !== DetectorChartData.index, 'highlightBg': chooseIndex === DetectorChartData.index }" :style="{'width': item.w + 'px', 'height': item.h + 'px'}">
        <div class="detector-statistics-echarts" :id="'detectorStatisticsEcharts' + DetectorChartData.index"></div>
        <!-- <div>
          <div class="detector-rect"
          :class="{'defaultVehicleBorder': DetectorData.detectortype === 1,
          'defaultPedBorder': DetectorData.detectortype === 2 }"></div>
        </div> -->
      </div>
      <div v-if="UsageMode === 'show'" class="centerText" :class="{'defaultBg': chooseIndex !== DetectorChartData.index, 'highlightBg': chooseIndex === DetectorChartData.index }" :style="{'width': item.w + 'px', 'height': item.h + 'px'}">
      </div>
    </drr>
  </div>
</template>
<script>
import * as echart from 'echarts'
import { getTheme } from '../../../../../utils/auth.js'
export default {
  name: 'countdownsvg',
  data () {
    return {
      defaultColor: 'DeepSkyBlue', // 默认状态颜色
      item: {},
      reset: true,
      Fontsize: 16, // 默认字号
      flowsaturation: [100],
      occupancysaturation: [50]
    }
  },
  watch: {
    item: {
      handler: function (newval, oldval) {
        if ((JSON.stringify(oldval) !== '{}')) {
          // 更改原数据的位置大小数据
          let data = {
            ...this.DetectorChartData,
            ...newval
          }
          let fields = Object.keys(newval)
          this.$emit('changeDetectorItem', data, fields)
        }
      }
    },
    DetectorChartData: {
      handler: function (newval, oldval) {
        console.log(this.Detector)
      }
    }
  },
  props: {
    DetectorChartData: {
      type: Object
    },
    Detector: {
      type: Array
    },
    isSeletable: {
      type: Boolean
    },
    chooseIndex: {
      type: Number
    },
    UsageMode: { // 当前图标模式： 绘制draw 展示show
      type: String,
      default: 'draw'
    }
  },
  methods: {
    boxDragStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
      this.$emit('handleSelectIcon', this.DetectorChartData)
    },
    resetSvg () {
      this.reset = false
      this.$nextTick(() => {
        this.reset = true
      })
    },
    handleSelectIcon (iconparams) {
      this.$emit('handleSelectIcon', this.DetectorChartData)
    },
    culculateFontsize () {
      // 根据倒计时图标大小，动态计算内部文字大小
      this.Fontsize = Math.floor(this.DetectorChartData.w / 140 * 20)
    },
    initEcharts () {
      this.detectorEcharts = echart.init(document.getElementById('detectorStatisticsEcharts' + this.DetectorChartData.index))
    },
    getEchartsData () {
      let option = {
        tooltip: {
          trigger: 'item'
        },
        grid: {
          left: '2%',
          right: '2%',
          top: '10',
          bottom: '10',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisLabel: {
            show: false,
            textStyle: {
              color: getTheme() === 'light' ? '#666666' : '#B9BABF'
            }
          },
          axisTick: {
            show: false,
            lineStyle: {
              color: getTheme() === 'light' ? '#D7DFE1' : '#30384D'
            }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: getTheme() === 'light' ? '#D7DFE1' : '#30384D'
            }
          },
          splitLine: {
            show: false
          }
        },
        yAxis: {
          type: 'value',
          max: 100,
          axisTick: {
            show: false
            // lineStyle: {
            //   color: getTheme() === 'light' ? '#D7DFE1' : '#30384D'
            // }
          },
          axisLine: {
            show: false,
            lineStyle: {
              color: getTheme() === 'light' ? '#D7DFE1' : '#30384D'
            }
          },
          splitLine: {
            show: false
            // lineStyle: {
            //   color: getTheme() === 'light' ? '#DCDFE6' : '#30384d'
            // }
          },
          splitArea: {
            show: false
            // areaStyle: {
            //   color: getTheme() === 'light' ? ['#fafafa', '#fff'] : ['#202940', '#1a2338']
            // }
          },
          axisLabel: {
            show: false,
            textStyle: {
              color: getTheme() === 'light' ? '#666666' : '#B9BABF'
            }
          }
        },
        series: [{
          type: 'bar',
          barWidth: '10',
          itemStyle: {
            color: 'red',
            borderWidth: 0,
            shadowBlur: {
              shadowColor: 'rgba(255,255,255,0.31)',
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowOffsetY: 2
            }
          },
          data: this.flowsaturation
        }, {
          type: 'bar',
          barWidth: '10',
          itemStyle: {
            color: 'green'
          },
          data: this.occupancysaturation
        }]
      }
      this.detectorEcharts.setOption(option)
    },
    handleTooltipEvent () {
      let _this = this
      this.detectorEcharts.on('mouseover', function (params) {
        _this.detectorEcharts.dispatchAction({
          type: 'showTip',
          seriesIndex: 0,
          dataIndex: params.dataIndex,
          position: ['0', '0']
        })
      })
      this.detectorEcharts.on('mouseout', function (params) {
        _this.detectorEcharts.dispatchAction({
          type: 'hideTip'
        })
      })
    }
  },
  created () {
    this.item = {
      x: this.DetectorChartData.x,
      y: this.DetectorChartData.y,
      w: this.DetectorChartData.w,
      h: this.DetectorChartData.h,
      angle: this.DetectorChartData.angle
    }
  },
  mounted () {
    this.initEcharts()
    this.getEchartsData()
    this.handleTooltipEvent()
  }
}
</script>
<style scoped>
.detector-statistics-echarts {
  height: 100%;
}
</style>
