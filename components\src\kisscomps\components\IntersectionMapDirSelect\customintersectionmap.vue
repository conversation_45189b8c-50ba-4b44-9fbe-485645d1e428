/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
<div class="custom-intersection-map">
  <div class="crossDirection-display openatc-intersection-base-map" :class="{
    'widescreenCrossImg': bodyDomWidth > 1680,
    'superlargeCrossImg': bodyDomWidth <= 1680 && bodyDomWidth > 1440,
    'largeCrossImg': bodyDomWidth <= 1440 && bodyDomWidth > 1280,
    'middleCrossImg2': bodyDomWidth <= 1280 && bodyDomWidth > 960,
    'smallCrossImg': bodyDomWidth <= 960 && bodyDomWidth > 890,
    'smallCrossImg2': bodyDomWidth <= 890 && bodyDomWidth > 720,
    'miniCrossImg': bodyDomWidth <= 720 && bodyDomWidth > 650,
    'superminiCrossImg': bodyDomWidth <= 650 && bodyDomWidth > 450,
    'transMiddleCrossImg': bodyDomWidth <= 450 && bodyDomWidth > 350,
    'transMiddleCrossImg2': bodyDomWidth <= 350 && bodyDomWidth > 300,
    'transMiddleCrossImg3': bodyDomWidth <= 300 && bodyDomWidth > 260,
    'transMiniCrossImg': bodyDomWidth <= 260,
    'changePaddingBottom': graphicMode }">
    <CustomCrossDiagram ref= "crossDiagram3" v-if="reset"
      :agentId="agentId"
      :choosedDirection="choosedDirection"
      :choosedPedDirection="choosedPedDirection"
      :clickMode="clickMode"
      :channelType="channelType"
      :isShowMessage ="isShowMessage"
      @handleClickCrossIcon="handleClickCrossIcon" />
  </div>
</div>
</template>

<script>
import CustomCrossDiagram from './customCrossDiagram'
import { setToken } from '../../../utils/auth'
export default {
  name: 'custom-intersection-base-map',
  components: {
    CustomCrossDiagram
  },
  data () {
    return {
      reset: false,
      bodyDomWidth: 352,
      bodyDomSize: {
        width: 1920,
        height: 1080
      }
    }
  },
  props: {
    agentId: {
      type: String
    },
    graphicMode: {
      type: Boolean,
      default: false
    },
    isShowInterval: {
      type: Boolean,
      default: true
    },
    isShowMessage: {
      type: Boolean,
      default: true
    },
    roadDirection: {
      type: String,
      default: 'right'
    },
    isShowState: {
      type: Boolean,
      devault: false
    },
    isShowMode: {
      type: Boolean,
      default: false
    },
    modeName: {
      type: String,
      default: ''
    },
    controlName: {
      type: String,
      default: ''
    },
    stateName: {
      type: String,
      default: ''
    },
    choosedDirection: {
      type: Array
    },
    choosedPedDirection: {
      type: Array
    },
    clickMode: { // 是否开启点击模式
      type: Boolean,
      default: false
    },
    channelType: {
      type: Boolean,
      default: false
    }
  },
  watch: {
    $route: {
      handler: function (val, oldVal) {
        if (val.query !== undefined && val.query.agentid !== undefined) {
          this.resetCrossDiagram()
        }
      },
      // 深度观察监听
      deep: true
    },
    channelType: {
      handler: function (val, oldVal) {
        if (val) {
          this.reset = true
        }
      }
    },
    agentId: {
      handler: function (val1, val2) {
        if (val1 !== val2 && val2 !== undefined) {
          this.resetCrossDiagram()
        }
      }
    }
  },
  created () {
    if (this.$route.query !== undefined && Object.keys(this.$route.query).length && this.$route.query.agentid !== undefined) {
      this.resetCrossDiagram()
    }
  },
  mounted () {
    this.getParentSize()
    this.reset = true
  },
  updated () {
  },
  methods: {
    resetCrossDiagram () {
      this.reset = false
      this.$nextTick(() => {
        this.reset = true
      })
    },
    getParentSize () {
      // 获取最外层dom尺寸，适配准备
      var _this = this
      this.$nextTick(function () {
        if (this.$el.parentElement === null || this.$el.parentElement === undefined) return
        this.bodyDomSize.width = this.$el.parentElement.clientWidth
        this.bodyDomWidth = this.bodyDomSize.width
        console.log(this.bodyDomWidth)
        window.addEventListener('resize', () => {
        // 定义窗口大小变更通知事件
          if (_this.$el.parentElement === null || _this.$el.parentElement === undefined) return
          _this.bodyDomSize.width = _this.$el.parentElement.clientWidth
          this.bodyDomWidth = this.bodyDomSize.width
          console.log(this.bodyDomWidth)
          console.log('resize this.bodyDomSize.width', _this.bodyDomSize.width)
        }, false)
      })
    },
    setPropsToken (token) {
      // 获取组件外传入的token，便于独立组件调用接口
      if (token && token !== '') {
        setToken(token)
      }
    },
    handleClickCrossIcon (allChoosedDir, curClickedPhase) {
      console.log(allChoosedDir, curClickedPhase)
      this.$emit('handleClickCrossIcon', allChoosedDir, curClickedPhase)
    }
  },
  destroyed () {
  }
}
</script>
