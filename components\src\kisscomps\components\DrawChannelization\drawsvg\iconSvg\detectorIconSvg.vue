/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div v-if="reset" class="detector-icon">
    <drr
      :style="{'z-index': chooseIndex === DetectorData.index ? 9 : 0}"
      :x="item.x"
      :y="item.y"
      :w="item.w"
      :h="item.h"
      :selected="chooseIndex === DetectorData.index"
      :selectable="isSeletable"
      :angle="item.angle"
      :aspectRatio="true"
      @select="handleSelectIcon(item)"
      @dragstop="boxDragStop(item, ...arguments)"
      @resize="boxResize(...arguments)"
      @resizestop="boxResizeStop(item, ...arguments)"
      @rotatestop="boxRotateStop(item, ...arguments)"
    >
    <div v-if="UsageMode === 'draw'">
      <div v-if="chooseIndex !== DetectorData.index" class="centerText defaultBg" :style="{'width': item.w + 'px', 'height': item.h + 'px'}">
        <div class="detectorIcon"
          :style="{'width': item.w + 'px', 'fontSize': Fontsize + 'px', 'lineHeight': item.h - 4 + 'px'}"
          :class="{'defaultVehicleBorder': DetectorData.detectortype === 1,
          'defaultPedBorder': DetectorData.detectortype === 2 }">{{DetectorData.detectorid}}</div>
      </div>
      <div v-if="chooseIndex === DetectorData.index" class="centerText highlightBg" :style="{'width': item.w + 'px', 'height': item.h + 'px'}">
        <div class="detectorIcon"
          :style="{'width': item.w + 'px', 'fontSize': Fontsize + 'px', 'lineHeight': item.h - 4 + 'px'}"
          :class="{'defaultVehicleBorder': CurChooseIcon.detectortype == 1,
          'defaultPedBorder': CurChooseIcon.detectortype == 2 }">{{CurChooseIcon.detectorid}}</div>
      </div>
    </div>

      <div v-if="UsageMode === 'show'" class="centerText" :style="{'width': item.w + 'px', 'height': item.h + 'px'}">
         <div class="detectorIcon"
          :style="{'width': item.w + 'px', 'fontSize': Fontsize + 'px','lineHeight': item.h - 4 + 'px', 'background': DetectorData.fillcolor}"
          :class="{'defaultVehicleBorder': DetectorData.detectortype == 1,
          'defaultPedBorder': DetectorData.detectortype == 2 }"
          >{{DetectorData.detectorid}}</div>
      </div>
    </drr>
  </div>
</template>
<script>
export default {
  name: 'countdownsvg',
  data () {
    return {
      defaultColor: 'DeepSkyBlue', // 默认状态颜色
      item: {},
      reset: true,
      Fontsize: 16 // 默认字号
    }
  },
  watch: {
    // item: {
    //   handler: function (newval, oldval) {
    //     if ((JSON.stringify(oldval) !== '{}')) {
    //       // 更改原数据的位置大小数据
    //       let data = {
    //         ...this.DetectorData,
    //         ...newval
    //       }
    //       let fields = Object.keys(newval)
    //       this.$emit('changeDetectorItem', data, fields)
    //     }
    //   }
    // }
    DetectorData: {
      handler: function (val) {
        if (this.UsageMode === 'draw') {
          this.item.x = val.x
          this.item.y = val.y
          this.item.angle = val.angle
        }
      },
      deep: true
    }

    // CurChooseIcon: {
    //   handler: function (data) {
    //     debugger
    //   },
    //   deep: true
    // }
  },
  props: {
    DetectorData: {
      type: Object
    },
    isSeletable: {
      type: Boolean
    },
    chooseIndex: {
      type: Number
    },
    UsageMode: { // 当前图标模式： 绘制draw 展示show
      type: String,
      default: 'draw'
    },
    CurChooseIcon: {
      type: Object
    }
  },
  methods: {
    boxDragStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
      this.$emit('handleSelectIcon', this.DetectorData)
      this.handleChangeData()
    },
    boxResize (final) {
      // this.resetSvg()
      this.item = JSON.parse(JSON.stringify(final))
      this.handleChangeData()
    },
    boxResizeStop (origin, final) {
      // this.resetSvg()
      // this.item = JSON.parse(JSON.stringify(final))
    },
    boxRotateStop (origin, final) {
      this.item = JSON.parse(JSON.stringify(final))
      // 第四象限的角度是负值（开源组件接口返回），转化为正值便于理解
      if (this.item.angle < 0) {
        this.item.angle = this.item.angle + 360
      }
      this.handleChangeData()
    },
    resetSvg () {
      this.reset = false
      this.$nextTick(() => {
        this.reset = true
      })
    },
    handleSelectIcon (iconparams) {
      this.$emit('handleSelectIcon', this.DetectorData)
    },
    culculateFontsize () {
      // 根据倒计时图标大小，动态计算内部文字大小
      this.Fontsize = Math.floor(this.DetectorData.w / 140 * 20)
    },
    handleChangeData () {
      let data = {
        ...this.DetectorData,
        ...this.item
      }
      let fields = Object.keys(this.item)
      this.$emit('changeDetectorItem', data, fields)
      this.handleSelectIcon()
    }
  },
  created () {
    this.item = {
      x: this.DetectorData.x,
      y: this.DetectorData.y,
      w: this.DetectorData.w,
      h: this.DetectorData.h,
      angle: this.DetectorData.angle
    }
  }
}
</script>
<style scoped>
.centerText {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 9;
  display: flex;
  align-items: center;
}
.detectorIcon {
  font-family: SourceHanSansCN-Regular;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #fff;
  margin: 0 auto;
  text-align: center;
  height: 100%;
}
.defaultBg {
  background-color: transparent;
}
.highlightBg {
  background-color: #299BCC;
}
.defaultVehicleBorder {
  border: 2px solid #00FF00;
}
.defaultPedBorder {
  border: 2px solid #0080FF;
}
.centerText .text {
  display: inline-block;
  color: #299BCC;
  margin-top: 20px;
}
</style>
