<template>
    <div class="boradCard">
        <StageStatus
        v-show="isPhase"
        :localPatternList="localPatternList"
        :contrloType="contrloType"
        :controlData="controlData"
        :patternStatusList="patternStatusList">
        </StageStatus>
        <PatternStatus
        :showBarrier="showBarrier"
        :controlPhase="controlPhase"
        :isShowTip="isShowTip"
        :isBorder="isBorder"
        :isMove="isMove"
        :localPatternList="localPatternList"
        :showCondition="showCondition"
        :contrloType="contrloType"
        :allPatternList="allPatternList"
        :stagesChange="stagesChange"
        :cycleChange="cycleChange"
        :patternList="patternList"
        :agentId="agentId"
        :controlData="controlData"
        :phaseList="phaseList"
        :cycle="cycle"
        :syncTime="syncTime"
        :style="{'margin-top': '25px'}"
        @handleSplitMove="handleSplitMove"
        :patternStatusList="patternStatusList"
        :patternId="patternId"
        :cycles="cycles">
        </PatternStatus>
    </div>
</template>

<script>
import PatternStatus from '../PatternStatus'
import StageStatus from '../StageStatus'
export default {
  name: 'board-card',
  components: {
    PatternStatus,
    StageStatus
  },
  data () {
    return {

    }
  },
  props: {
    contrloType: {
      type: String
    },
    cycleChange: {
      type: Boolean,
      default: false
    },
    controlPhase: {
      type: Object
    },
    allPatternList: {
      type: Array
    },
    localPatternList: {
      type: Array
    },
    showCondition: {
      type: Boolean,
      default: false
    },
    patternList: {
      type: Array
    },
    stagesChange: {
      type: Array
    },
    isMove: {
      type: Boolean,
      default: true
    },
    isBorder: {
      type: Boolean,
      default: false
    },
    isShowTip: {
      type: Boolean,
      default: true
    },
    showBarrier: {
      type: Boolean,
      default: false
    },
    phaseList: {
      type: Array
    },
    controlData: {
      type: Object
    },
    patternStatusList: {
      type: Array
    },
    patternId: {
      type: Number
    },
    agentId: {
      type: String
    },
    isPhase: {
      type: Boolean,
      default: false
    },
    cycles: {
      type: Number
    },
    cycle: {
      type: Number
    },
    syncTime: {
      type: Number
    }
  },
  methods: {
    handleSplitMove (data) {
      this.$emit('handleSplitMove', data)
    }
  }
}
</script>

<style>

</style>
