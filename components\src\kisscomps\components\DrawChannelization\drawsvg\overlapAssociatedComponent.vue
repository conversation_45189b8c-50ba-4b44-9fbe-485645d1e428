/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="phase-associated-component">
    <div class="phase-box" v-for="(item, index) in overlaplList" :key="index">
      <div class="single-phase" @click="selectPhase(index, item)" :class="preselectPhase == item.id ? 'single-phase-select' : ''">
        <div class="ped-icon">
          <!-- <PatternWalkSvg v-if="item.peddirection && item.peddirection.length" :showWalk="getshowped(item.peddirection)" Width="42" Height="44" /> -->
        </div>

        <div class="phase-icon" v-if="item.direction">
          <xdr-dir-selector :Data="Data" Width="60px" Height="60px" Widths="60px" Heights="60px" :showlist="getShowlist(item)" :roadDirection="roadDirection"></xdr-dir-selector>
        </div>
      </div>
      <div class="single-phase-name">{{$t('openatccomponents.channelizationmap.phase') + item.id}}</div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
import PatternWalkSvg from '../../PatternWalkSvg/PatternWalkSvg'
import PhaseDataModel from '../../IntersectionMap/crossDirection/utils.js'
export default {
  name: 'phase-associated-component',
  components: {
    PatternWalkSvg
  },
  data () {
    return {
      preselectPhase: -1,
      id: 1,
      isCanAdd: true,
      Data: {
        left: '4px',
        top: '3px'
      }
    }
  },
  props: {
    editData: {
      type: Object
    }
  },
  computed: {
    ...mapState({
      overlaplList: state => state.globalParam.tscParam.overlaplList,
      roadDirection: state => state.globalParam.roadDirection
    })
  },
  watch: {
    overlaplList: {
      handler: function (list) {
      },
      deep: true
    },
    editData: {
      handler: function (editobj) {
        this.initChoosedPhase(editobj)
      },
      deep: true
    }
  },
  methods: {
    initChoosedPhase (editobj) {
      if (editobj.phaseid !== undefined && editobj.phasetype === 'overlap') {
        this.preselectPhase = editobj.phaseid
      } else {
        this.preselectPhase = -1
      }
    },
    selectPhase (index, item) {
      // 关联相位
      this.preselectPhase = item.id
      this.$emit('selectPhaseNew', this.preselectPhase, 'overlap')
    },
    changeDirection (choosedicon) {

    },
    editDirPosToPhaseDireaciton (laneDirArr, lanePos) {
      // 编辑的车道转向和方位，转化成相位的direction
      let phaseDireacitonArr = laneDirArr.map(lanedir => {
        if (lanePos === 1) {
          // 东
          return lanedir
        }
        if (lanePos === 2) {
          // 西
          return 4 * 1 + lanedir
        }
        if (lanePos === 3) {
          // 南
          return 4 * 3 + lanedir
        }
        if (lanePos === 4) {
          // 北
          return 4 * 2 + lanedir
        }
      })
      return phaseDireacitonArr
    },
    getShowlist (data) {
      if (data.direction && data.direction.length > 0) {
        return data.direction.map(dir => {
          return {
            id: dir,
            peddirection: this.getshowped(data.peddirection),
            color: '#1E1E1E'
          }
        })
      } else {
        return [
          {
            id: '',
            peddirection: this.getshowped(data.peddirection),
            color: '#1E1E1E'
          }
        ]
      }
    },
    getshowped (peddirection) {
      let peddirarr = peddirection.map(peddir => ({
        id: peddir,
        name: this.PhaseDataModel.getSidePos(peddir).name
      }))
      return peddirarr
    },
    handleDisassociatePhase (deletePhaseid) {
      // 删除相位后，解除相位关联
      this.$emit('handleDisassociatePhase', deletePhaseid)
    },
    getPedPhasePos () {
    // 行人相位信息
      this.sidewalkPhaseData = []
      this.overlaplList.forEach((ele, i) => {
        if (ele.peddirection) {
          ele.peddirection.forEach((dir, index) => {
            // 行人相位
            if (this.PhaseDataModel.getSidePos(dir)) {
              this.sidewalkPhaseData.push({
                key: this.CrossDiagramMgr.getUniqueKey('pedphase'),
                phaseid: ele.id, // 相位id，用于对应相位状态
                id: dir,
                name: this.PhaseDataModel.getSidePos(dir).name
              })
            }
          })
        }
      })
      return this.sidewalkPhaseData
    }
  },
  created () {
    this.PhaseDataModel = new PhaseDataModel()
  },
  mounted () {
    this.initChoosedPhase(this.editData)
  },
  destroyed () {
  }
}
</script>
<style lang="scss">
</style>
