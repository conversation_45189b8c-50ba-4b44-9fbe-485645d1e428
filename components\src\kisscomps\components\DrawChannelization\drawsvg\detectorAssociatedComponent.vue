/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div class="detector-associated-component">
    <div class="detector-box" v-for="(item, index) in preDetList" :key="index">
      <div class="single-detector" @click="selectAssociatedDetector(index, item)" :class="preselectDetector == item.id ? 'single-detector-select' : ''">
        {{item.id}}
      </div>
    </div>
  </div>
</template>
<script>
import { mapState } from 'vuex'
export default {
  name: 'detector-associated-component',
  data () {
    return {
      preselectDetector: -1,
      id: 1,
      isCanAdd: true,
      preDetList: []
    }
  },
  props: {
    editData: {
      type: Object
    }
  },
  computed: {
    ...mapState({
      detectorList: state => state.globalParam.tscParam.detectorList,
      peddetectorList: state => state.globalParam.tscParam.pedestrainDetectorList,
      roadDirection: state => state.globalParam.roadDirection
    })
  },
  watch: {
    detectorList: {
      handler: function (list) {
      },
      deep: true
    },
    editData: {
      handler: function (editobj) {
        this.loadCurDetectorList(editobj)
        this.initChoosedDetector(editobj)
      },
      deep: true
    }
  },
  methods: {
    initChoosedDetector (editobj) {
      if (editobj.detectorid !== undefined) {
        this.preselectDetector = editobj.detectorid
      } else {
        this.preselectDetector = -1
      }
    },
    selectAssociatedDetector (index, item) {
      // 关联相位
      this.preselectDetector = item.id
      this.$emit('selectAssociatedDetector', this.preselectDetector)
    },
    handleDisassociateDetector (deleteDetectorid) {
      // 删除相位后，解除相位关联
      this.$emit('handleDisassociateDetector', deleteDetectorid)
    },
    loadCurDetectorList (editobj) {
      this.detectortype = editobj.detectortype
      if (editobj.detectortype === 1) {
        // 车辆检测器
        this.preDetList = JSON.parse(JSON.stringify(this.detectorList))
      }
      if (editobj.detectortype === 2) {
        // 行人检测器
        this.preDetList = JSON.parse(JSON.stringify(this.peddetectorList))
      }
    }
  },
  created () {
  },
  mounted () {
    this.loadCurDetectorList(this.editData)
    this.initChoosedDetector(this.editData)
  },
  destroyed () {
  }
}
</script>
<style lang="scss">
.detector-associated-component {
  width: 100%;
  height: auto;
  overflow: hidden;
  .detector-box {
    float: left;
    margin-top: 10px;
      .single-detector {
        margin-right: 10px;
        text-align: center;
        cursor:pointer;
        width: 35px;
        height: 35px;
        background-color: #edf6ff;
        border-radius: 6px;
        line-height: 35px;
        position: relative;
      }
      .single-detector-select {
        background-color: #a2cfff;
      }
      .single-detector-name {
        width: 70PX;
        margin-top: 3PX;
        font-size: 12PX;
        font-weight: normal;
        font-stretch: normal;
        line-height: 22PX;
        letter-spacing: 0PX;
        color: #606266;
        text-align: center;
        height: 40PX;
      }
  }

  .add-detector {
    padding-top: 23px;
  }
  .icon-fangda {
    font-size: 24px;
    color: #299BCC;
  }
  .add-disabled {
    color: #bcbec2;
    background-color: #f4f4f5;
  }
  .special-lane {
    display:flex;
    flex-direction:row;
    justify-content:center;
    align-items:center;
  }
}
</style>
