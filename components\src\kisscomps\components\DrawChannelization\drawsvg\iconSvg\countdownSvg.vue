/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
<template>
  <div v-if="reset" class="countdown-icon">
    <drr
      style="z-index: 10"
      :x="item.x"
      :y="item.y"
      :w="item.w"
      :h="item.h"
      :selected="chooseIndex === CountdownData.index"
      :selectable="isSeletable"
      :angle="item.angle"
      :aspectRatio="true"
      :rotatable="false"
      @select="handleSelectIcon(item)"
      @dragstop="boxDragStop(item, ...arguments)"
      @resize="boxResizeStop"
      @rotatestop="boxRotateStop(item, ...arguments)"
    >
      <div v-if="UsageMode === 'draw'" class="centerText" :class="{'defaultBg': chooseIndex !== CountdownData.index, 'highlightBg': chooseIndex === CountdownData.index }" :style="{'width': item.w + 'px', 'height': item.h + 'px'}">
        <div class="phaseCountdown drawPhaseCountdown" :style="{'width': item.w + 'px', 'fontSize': textFontSize ? textFontSize + 'px' : Fontsize + 'px'}">
          {{$t('openatccomponents.channelizationmap.countdown')}}
        </div>
      </div>
      <div v-if="UsageMode === 'show' && isHasPhase && !customText" class="centerText showCenterText" :class="{'defaultBg': chooseIndex !== CountdownData.index, 'highlightBg': chooseIndex === CountdownData.index }" :style="{'width': item.w + 'px', 'height': item.h + 'px', 'backgroundColor': bcgColor}">
        <div class="phaseCountdown" :style="{'width': item.w + 'px', 'fontSize': textFontSize ? textFontSize + 'px' : Fontsize + 'px'}">
          <div v-if="phaseCountdownList.length > 0">
            <div v-for="curPhase in phaseCountdownList" :key="curPhase.id" :style="{color: curPhase.phaseCountdownColor}">
              <span style="float: left;color: #fff;margin-right: 5px;">{{$t('openatccomponents.overview.phase')}}{{curPhase.id}}:</span>
              <span style="float: left;">{{curPhase.phaseCountdown}}</span>
            </div>
          </div>
        </div>
      </div>
      <div v-if="UsageMode === 'show' && customText"
        class="defaultBg"
        :style="{'width': item.w + 'px', 'height': item.h + 'px', 'fontSize': textFontSize ? textFontSize + 'px' : Fontsize + 'px', 'backgroundColor': bcgColor}">
        <div class="customText">{{customText}}</div>
      </div>
    </drr>
  </div>
</template>
<script>
export default {
  name: 'countdownsvg',
  data () {
    return {
      defaultColor: '#ccc', // 默认状态颜色
      item: {},
      reset: true,
      phaseCountdownList: [],
      phaseCountdownIcon: 999,
      Fontsize: 20, // 倒计时默认字号
      minCountdownSize: 30 // 限制最小倒计时区域大小
    }
  },
  watch: {
  // item: {
    //   handler: function (newval, oldval) {
    //     if ((JSON.stringify(oldval) !== '{}')) {
    //       // 更改原数据的位置大小数据
    //       let data = {
    //         ...this.CountdownData,
    //         ...newval
    //       }
    //       let fields = Object.keys(newval)
    //       this.$emit('changeTimeItem', data, fields)
    //     }
    //   }
    // },
    CountdownData: {
      handler: function (val) {
        if (this.UsageMode === 'draw') {
          this.item.x = val.x
          this.item.y = val.y
          this.item.angle = val.angle
        }
      },
      deep: true
    },
    CountdownList: {
      handler: function (val) {
        // 接收倒计时数据进行显示
        this.phaseCountdownList = val
        this.culculateFontsize()
      },
      deep: true
    }
  },
  props: {
    CountdownData: {
      type: Object
    },
    isSeletable: {
      type: Boolean
    },
    CountdownList: {
      type: Array
    },
    chooseIndex: {
      type: Number
    },
    UsageMode: { // 当前图标模式： 绘制draw 展示show
      type: String,
      default: 'draw'
    },
    isHasPhase: {
      type: Boolean,
      default: true
    },
    showCustomText: {
      type: Boolean
    },
    customText: { // 圆内所显示自定义文字内容，如果传了，显示优先级高于倒计时文字
      type: String
    },
    bcgColor: { // 圆背景色
      type: String,
      default: 'rgba(94, 90, 90, 0.8)'
    },
    textFontSize: { // 所显示文字大小，如果传了，优先级高于文字按宽度适配大小
      type: String
    }
  },
  methods: {
    boxDragStop (origin, final) {
      // let info = this.handleMinCountdownSize(final)
      this.item = JSON.parse(JSON.stringify(final))
      this.$emit('handleSelectIcon', this.CountdownData)
      this.handleChangeData()
    },
    boxResizeStop (final) {
      // let info = this.handleMinCountdownSize(final)
      // this.resetSvg()
      this.item = JSON.parse(JSON.stringify(final))
      this.handleChangeData()
    },
    boxRotateStop (origin, final) {
      // let info = this.handleMinCountdownSize(final)
      this.item = JSON.parse(JSON.stringify(final))
      this.handleChangeData()
    },
    resetSvg () {
      this.reset = false
      this.$nextTick(() => {
        this.reset = true
      })
    },
    handleSelectIcon (iconparams) {
      this.$emit('handleSelectIcon', this.CountdownData)
    },
    culculateFontsize () {
      // 根据倒计时图标大小，动态计算内部文字大小
      if (this.textFontSize) return
      this.Fontsize = Math.floor(this.CountdownData.w / 140 * 20)
      if (this.Fontsize < 14) {
        this.Fontsize = 14
      }
    },
    handleChangeData () {
      let data = {
        ...this.CountdownData,
        ...this.item
      }
      let fields = Object.keys(this.item)
      this.$emit('changeTimeItem', data, fields)
      this.handleSelectIcon()
    },
    handleMinCountdownSize (final) {
      let info = JSON.parse(JSON.stringify(final))
      if (info.w < 30 || info.h < 30) {
        info.w = 30
        info.h = 30
      }
      return info
    }
  },
  created () {
    this.item = {
      x: this.CountdownData.x,
      y: this.CountdownData.y,
      w: this.CountdownData.w,
      h: this.CountdownData.h,
      angle: this.CountdownData.angle
    }
  }
}
</script>
<style scoped>
.centerText {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translateX(-50%) translateY(-50%);
  z-index: 9;
  display: flex;
  align-items: center;
}
.showCenterText {
  padding-left: 16px;
}
.phaseCountdown {
  font-family: SourceHanSansCN-Regular;
  font-weight: normal;
  font-stretch: normal;
  letter-spacing: 0px;
  color: #fff;
  margin: 0 auto;
}
.drawPhaseCountdown {
  text-align: center;
}
.defaultBg {
  border-radius: 50%;
  background-color: rgba(94, 90, 90, 0.8);
}
.highlightBg {
  border-radius: 50%;
  background-color: #299BCC;
}
.centerText .text {
  display: inline-block;
  color: #299BCC;
  margin-top: 20px;
}
</style>
