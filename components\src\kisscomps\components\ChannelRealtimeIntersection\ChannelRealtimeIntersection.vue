/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
  <!--通道实时状态路口图-->
<template>
  <div class="channel-realtime-intersection">
    <IntersectionMap
      v-if="reset"
      ref="intersectionMap"
      channelType
      :crossStatusData="crossStatusData"
      :agentId="agentId"
      :graphicMode="true"
      :roadDirection="roadDirection"
      :isThirdSignal="isThirdSignal"/>
</div>
</template>

<script>
import IntersectionMap from '../IntersectionMap/intersectionmap'
import { queryDevice } from '../../../api/control.js'
import { getMessageByCode } from '../../../utils/responseMessage.js'

export default {
  name: 'channel-realtime-intersection',
  components: {
    IntersectionMap
  },
  computed: {
  },
  props: {
    agentId: {
      type: String,
      default: '0'
    },
    roadDirection: {
      type: String,
      default: 'right'
    },
    channelRealtimeStatusData: {
      type: Object
    }
  },
  watch: {
    agentId: {
      handler: function (val) {
        if (val) {
          this.reset = false
          this.$nextTick(() => {
            this.reset = true
          })
        }
      },
      deep: true
    },
    resizeMap: {
      handler: function (newval, oldval) {
        if (newval === true && oldval === false) {
          this.getParentSize()
        }
      }
    },
    channelRealtimeStatusData: {
      handler: function (val) {
        // 相位统计数据数据
        this.crossStatusData = JSON.parse(JSON.stringify(val))
      },
      // 深度观察监听
      deep: true
    }
  },
  data () {
    return {
      reset: true,
      crossStatusData: {},
      isThirdSignal: false
    }
  },
  methods: {
    getPlatform () {
      queryDevice(this.agentId).then(res => {
        if (!res.data.success) {
          let commomMsg = this.$t('openatccomponents.overview.signalID') + ': ' + this.AgentId
          this.$message.error(getMessageByCode(res.data.code, this.$i18n.locale) + ' - ' + commomMsg)
          return
        }
        this.platform = res.data.data.platform
        if (this.platform !== '' && this.platform !== 'OpenATC') {
          this.isThirdSignal = true
        } else {
          this.isThirdSignal = false
        }
        if (this.thirdSignal) {
          this.isThirdSignal = this.thirdSignal
        }
      })
    }
  },
  created () {
  },
  mounted () {
    // setTimeout(() => {
    // 模拟
    //   this.crossStatusData = JSON.parse(JSON.stringify(this.channelRealtimeStatusData))
    // }, 3000)
    this.getPlatform()
    this.crossStatusData = JSON.parse(JSON.stringify(this.channelRealtimeStatusData))
  },
  destroyed () {
  }
}
</script>

<style lang="css" rel="stylesheet/scss">
</style>
