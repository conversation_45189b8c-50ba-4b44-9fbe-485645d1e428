/**
 * Copyright (c) 2020 kedacom
 * OpenATC is licensed under Mulan PSL v2.
 * You can use this software according to the terms and conditions of the Mulan PSL v2.
 * You may obtain a copy of Mulan PSL v2 at:
 * http://license.coscl.org.cn/MulanPSL2
 * THIS SOFTWARE IS PROVIDED ON AN "AS IS" BASIS, WITHOUT WARRANTIES OF ANY KIND,
 * EITHER EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO NON-INFRINGEMENT,
 * MERCHANTABILITY OR FIT FOR A PARTICULAR PURPOSE.
 * See the Mulan PSL v2 for more details.
 **/
import Authapi from './authapi'

export const saveChannelizatonChart = (data) => {
  let api = new Authapi('saveChannelizatonChart')
  return api.Send({}, data, [])
}

export const getDuration = (data) => {
  let api = new Authapi('gettimeplan')
  return api.Send({}, data, [])
}

export const getChannelizatonChart = (agentid) => {
  let api = new Authapi('getChannelizatonChart')
  return api.Send({}, {}, [agentid])
}

export const getDetectorStatus = (agentid) => {
  let api = new Authapi('getDetectorStatus')
  return api.Send({}, {}, [agentid])
}

export function getTscPhase (agentid) {
  let api = new Authapi('getTscControl')
  let data = {}
  data.agentid = agentid
  data.operation = 'get-request'
  data.infotype = 'feature/phase'
  return api.Send({}, data)
}

export const getValidDirections = (agentid) => {
  let api = new Authapi('getValidDirections')
  let paramList = [agentid]
  return api.Send({}, {}, [paramList])
}

export const getMergeDirections = (agentid) => {
  let api = new Authapi('getMergeDirections')
  let paramList = [agentid]
  return api.Send({}, {}, [paramList])
}

export default {
  saveChannelizatonChart,
  getChannelizatonChart,
  getDetectorStatus,
  getDuration,
  getTscPhase,
  getValidDirections
}
