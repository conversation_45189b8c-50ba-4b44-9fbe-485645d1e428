<template>
  <div class="common-board-column">
    <div class="common-board-column-header">
      {{headerText}}
    </div>
    <div class="common-board-table-header">
      <el-row :gutter="13">
        <el-col :span="4">{{this.$t('openatccomponents.overview.phase')}}
        </el-col>
        <el-col :span="10">{{this.$t('openatccomponents.overview.phasesplit')}}
        </el-col>
        <el-col :span="10">{{this.$t('openatccomponents.detector.mode')}}
        </el-col>
      </el-row>
    </div>
    <draggable
      class="common-board-column-content"
      :list="list"
      :options="options">
      <div class="common-board-item" v-for="element in list" :key="element.id">
        <el-row :gutter="13">
          <el-col :span="4">
            <el-tooltip class="item" effect="dark" placement="left">
              <div slot="content">{{element.name}}</div>
              <div class="common-phase-description">
                <xdrdirselector :Data="styles" Width="70px" Height="70px" Widths="50px" Heights="50px" :showlist="element.desc" :ISActiveMask="ISActiveMask" :MaskColor="MaskColor"></xdrdirselector>
              </div>
            </el-tooltip>
        </el-col>
        <el-col :span="10">
          <el-input-number :controls="false" class="col-content" size="small" :min="element.minSplit" :max="65535" :step="1" v-model.number="element.value" ref="type"></el-input-number>
        </el-col>
        <el-col :span="10">
          <el-select v-model="element.mode" class="col-content"  size="small" @change="doChange(element)" :placeholder="$t('openatccomponents.common.select')">
            <el-option
              v-for="item in modeOption"
              :key="item.value"
              :label="$t('openatccomponents.pattern.modeOption' + item.value)"
              :value="item.value">
            </el-option>
          </el-select>
        </el-col>
        </el-row>
      </div>
    </draggable>
  </div>
</template>
<script>
import draggable from 'vuedraggable'
import xdrdirselector from '../XRDDirSelector/XRDDirSelector'

export default {
  name: 'kan-ban',
  components: {
    draggable,
    xdrdirselector
  },
  data () {
    return {
      styles: {
        left: '1px',
        top: '0'
      },
      modeOption: [{
        value: 1
      }, {
        value: 2
      }, {
        value: 3
      }, {
        value: 4
      }, {
        value: 5
      }, {
        value: 6
      }, {
        value: 7
      }, {
        value: 8
      }, {
        value: 9
      }, {
        value: 10
      }, {
        value: 11
      }],
      coordphaseOption: [{
        value: 1
      }, {
        value: 2
      }, {
        value: 4
      }]
    }
  },
  props: {
    headerText: {
      type: String,
      default: 'Header'
    },
    options: {
      type: Object,
      default () {
        return {}
      }
    },
    phaseList: {
      type: Array
    },
    list: {
      type: Array,
      default () {
        return []
      }
    },
    index: {
      type: Number
    },
    ISActiveMask: {
      type: Boolean,
      default: true
    },
    // 当phase的描述为空时，显示的图形颜色。
    MaskColor: {
      type: String,
      default: '#000000'
    }
  },
  created () {
    // this.addMinSplit()
    // console.log(this.list, 'list')
  },
  watch: {
    list: {
      handler: function () {
        let newList = this.list
        // let list = this.$refs.type
        // let cycle = 0
        let n = this.index
        // for (let i = 0; i < list.length; i++) {
        //   cycle = cycle + Number(list[i].currentValue)
        // }
        // const globalParamModel = this.$store.getters.globalParamModel
        // let MaxCycle = globalParamModel.getParamsByType('patternList')[n].cycle
        // let pattern = globalParamModel.getParamsByType('patternList')[n]
        // globalParamModel.getParamsByType('patternList')[n].cycle = this.getMaxCycle(pattern)
        // this.addMinSplit()
        this.$emit('handleSplit', n, newList)
      },
      deep: true
    }
  },
  methods: {
    addMinSplit () {
    //   const globalParamModel = this.$store.getters.globalParamModel
    //   let phaseList = globalParamModel.getParamsByType('phaseList')
      for (let ls of this.list) {
        let phase = this.phaseList.filter((item) => {
          return item.id === ls.id
        })[0]
        if (!phase.redyellow) {
          phase.redyellow = 0
        }
        if (!phase.yellow) {
          phase.yellow = 0
        }
        if (!phase.redclear) {
          phase.redclear = 0
        }
        if (!phase.flashgreen) {
          phase.flashgreen = 0
        }
        if (!phase.phasewalk) {
          phase.phasewalk = 0
        }
        if (!phase.pedclear) {
          phase.pedclear = 0
        }
        // let temp1 = phase.redyellow + phase.yellow + phase.redclear + phase.flashgreen // 绿信比的最小值要大于最小绿+黄灯+全红+绿闪
        // let temp2 = phase.phasewalk + phase.pedclear
        // if (temp1 > temp2) {
        //   ls.minSplit = temp1
        // } else {
        //   ls.minSplit = temp2
        // }
        // if (ls.mode !== 7 && ls.value < ls.minSplit) {
        //   ls.value = ls.minSplit
        // }
        // let temp1 = phase.yellow + phase.redclear + phase.flashgreen // 绿信比的最小值要大于最小绿+黄灯+全红+绿闪
        let temp1 = phase.yellow + phase.redclear + phase.mingreen
        let temp2 = phase.yellow + phase.redclear + phase.phasewalk + phase.pedclear
        ls.minSplit = temp1 > temp2 ? temp1 : temp2
        if (ls.mode !== 7 && ls.value < ls.minSplit) {
          ls.value = ls.minSplit
          // this.$message.error(this.$t('openatccomponents.pattern.splitCheckMsg'))
        }
      }
    },
    getMaxCycle (pattern) {
      let rings = pattern.rings
      let maxCycle = 0
      for (let ring of rings) {
        if (ring.length === 0) continue
        let cycle = 0
        for (let r of ring) {
          if (r.mode === 7) { // 忽略相位不计周期
            continue
          }
          cycle = cycle + r.value
        }
        if (cycle > maxCycle) {
          maxCycle = cycle
        }
      }
      return maxCycle
    },
    doChange (e) {
      if (e) {
        let n = this.index
        this.$emit('handleMode', n)
        this.$forceUpdate()
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.col-content {
  width: 100%;
}
</style>
